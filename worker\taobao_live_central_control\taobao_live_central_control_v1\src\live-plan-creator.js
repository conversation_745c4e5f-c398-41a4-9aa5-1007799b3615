import crypto from 'crypto';
import { database } from './database.js';
import { extractH5Token } from './utils/cookie-utils.js';

/**
 * 直播计划创建工具
 */
class LivePlanCreator {
    constructor() {
        this.appKey = '12574478';
        this.jsv = '2.7.4';
    }

    /**
     * MD5哈希函数
     * @param {string} string 
     * @returns {string}
     */
    md5(string) {
        return crypto.createHash('md5').update(string, 'utf8').digest('hex');
    }

    /**
     * 生成签名
     * @param {string} h5Token 
     * @param {number} timestamp 
     * @param {object} data 
     * @returns {string}
     */
    generateSign(h5Token, timestamp, data) {
        const dataStr = JSON.stringify(data);
        const signString = `${h5Token}&${timestamp}&${this.appKey}&${dataStr}`;
        return this.md5(signString);
    }

    /**
     * 构建请求URL
     * @param {string} api 
     * @param {string} version 
     * @param {string} h5Token 
     * @param {object} data 
     * @returns {object}
     */
    buildRequestUrl(api, version, h5Token, data) {
        const timestamp = Date.now();
        const sign = this.generateSign(h5Token, timestamp, data);
        
        const params = new URLSearchParams({
            jsv: this.jsv,
            appKey: this.appKey,
            t: timestamp.toString(),
            sign: sign,
            api: api,
            v: version,
            preventFallback: 'true',
            type: 'json',
            dataType: 'json',
            data: JSON.stringify(data)
        });

        return {
            url: `https://h5api.m.taobao.com/h5/${api}/${version}/?${params.toString()}`,
            timestamp,
            sign
        };
    }

    /**
     * 获取直播间列表
     * @param {string} h5Token 
     * @param {string} fullCookie 
     * @returns {Promise<object>}
     */
    async getRoomList(h5Token, fullCookie) {
        try {
            const api = 'mtop.taobao.dreamweb.room.list';
            const version = '1.0';
            const data = {};

            const { url } = this.buildRequestUrl(api, version, h5Token, data);

            const headers = {
                'Accept': 'application/json',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Referer': 'https://liveplatform.taobao.com/restful/index/live/list',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
                'Cookie': fullCookie
            };

            console.log(`🌐 获取直播间列表 URL: ${url}`);

            const response = await fetch(url, {
                method: 'GET',
                headers: headers
            });

            const responseText = await response.text();
            console.log(`📥 获取直播间列表响应: ${responseText}`);

            // 直接解析JSON响应
            const result = JSON.parse(responseText);

            // 检查API调用结果
            if (result.ret && Array.isArray(result.ret) && result.ret[0] === 'SUCCESS::调用成功') {
                if (result.data && result.data.rooms && result.data.rooms.length > 0) {
                    return {
                        success: true,
                        roomNum: result.data.rooms[0].roomNum,
                        roomData: result.data.rooms[0]
                    };
                } else {
                    return {
                        success: false,
                        error: '未找到可用的直播间'
                    };
                }
            } else {
                const errorMsg = result.ret ? result.ret[0] : '未知错误';
                return {
                    success: false,
                    error: `API错误: ${errorMsg}`
                };
            }

        } catch (error) {
            console.error('获取直播间列表失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 查询直播间信息
     * @param {string} h5Token 
     * @param {string} fullCookie 
     * @param {string} roomNum 
     * @returns {Promise<object>}
     */
    async queryRoomInfo(h5Token, fullCookie, roomNum) {
        try {
            const api = 'mtop.taobao.dreamweb.live.memory.query';
            const version = '1.0';
            const data = {
                roomNum: roomNum,
                platform: 'Web'
            };

            const { url } = this.buildRequestUrl(api, version, h5Token, data);

            const headers = {
                'Accept': 'application/json',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Referer': 'https://liveplatform.taobao.com/restful/index/live/list',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
                'Cookie': fullCookie
            };

            console.log(`🌐 查询直播间信息 URL: ${url}`);

            const response = await fetch(url, {
                method: 'GET',
                headers: headers
            });

            const responseText = await response.text();
            console.log(`📥 查询直播间信息响应: ${responseText.substring(0, 500)}...`);

            // 直接解析JSON响应
            const result = JSON.parse(responseText);

            // 检查API调用结果
            if (result.ret && Array.isArray(result.ret) && result.ret[0] === 'SUCCESS::调用成功') {
                if (result.data) {
                    return {
                        success: true,
                        roomInfo: result.data
                    };
                } else {
                    return {
                        success: false,
                        error: '查询直播间信息失败：无数据'
                    };
                }
            } else {
                const errorMsg = result.ret ? result.ret[0] : '未知错误';
                return {
                    success: false,
                    error: `API错误: ${errorMsg}`
                };
            }

        } catch (error) {
            console.error('查询直播间信息失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 创建直播计划
     * @param {string} h5Token 
     * @param {string} fullCookie 
     * @param {string} roomNum 
     * @param {object} roomInfo 
     * @param {number} appointmentTime 
     * @param {number} liveEndTime 
     * @returns {Promise<object>}
     */
    async createLivePlan(h5Token, fullCookie, roomNum, roomInfo, appointmentTime, liveEndTime) {
        try {
            const api = 'mtop.taobao.dreamweb.live.createpre';
            const version = '2.0';

            // 构建创建直播计划的数据
            const data = {
                roomNum: roomNum,
                landscape: false,
                title: roomInfo.title || '当季新款 时尚美丽',
                descInfo: '',
                coverImg: roomInfo.pitCoverList && roomInfo.pitCoverList[0] ? 
                    roomInfo.pitCoverList[0].coverRatioUrlMap.imgUrl : '',
                useCustomPullUrl: false,
                customPullUrl: '',
                onlyPrivacy: false,
                roomType: '0',
                appId: '',
                notice: '',
                uploadId: '',
                uploadStr: '',
                videoCoverPicUrl: '',
                coverVideoImg: '',
                showLocation: roomInfo.showLocation || 'false',
                digitalAnchorLive: 'false',
                appointmentTime: appointmentTime.toString(),
                liveEndTime: liveEndTime.toString(),
                itemListJson: '[]',
                extParamMapJson: JSON.stringify({
                    cover_3x4: roomInfo.pitCoverList && roomInfo.pitCoverList[0] ? 
                        roomInfo.pitCoverList[0].coverRatioUrlMap.imgUrl_3x4 : ''
                }),
                syncPreview: roomInfo.syncPreview || true,
                previewTitle: roomInfo.title || '当季新款 时尚美丽',
                previewTime: appointmentTime,
                previewCover: roomInfo.pitCoverList && roomInfo.pitCoverList[0] ? 
                    roomInfo.pitCoverList[0].coverRatioUrlMap.imgUrl : ''
            };

            const { url } = this.buildRequestUrl(api, version, h5Token, data);

            const headers = {
                'Accept': 'application/json',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Referer': 'https://liveplatform.taobao.com/restful/index/live/list',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
                'Cookie': fullCookie
            };

            const body = `data=${encodeURIComponent(JSON.stringify(data))}`;

            console.log(`🌐 创建直播计划 URL: ${url}`);
            console.log(`📤 创建直播计划数据: ${JSON.stringify(data, null, 2)}`);

            const response = await fetch(url, {
                method: 'POST',
                headers: headers,
                body: body
            });

            const responseText = await response.text();
            console.log(`📥 创建直播计划响应: ${responseText}`);

            // 直接解析JSON响应
            const result = JSON.parse(responseText);

            // 检查API调用结果
            if (result.ret && Array.isArray(result.ret) && result.ret[0] === 'SUCCESS::调用成功') {
                if (result.data && result.data.liveId) {
                    return {
                        success: true,
                        liveId: result.data.liveId
                    };
                } else {
                    return {
                        success: false,
                        error: '创建直播计划失败：无liveId返回'
                    };
                }
            } else {
                const errorMsg = result.ret ? result.ret[0] : '未知错误';
                return {
                    success: false,
                    error: `API错误: ${errorMsg}`
                };
            }

        } catch (error) {
            console.error('创建直播计划失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 为指定主播创建直播计划
     * @param {string} anchorName 
     * @param {number} appointmentTime 
     * @param {number} liveEndTime 
     * @returns {Promise<object>}
     */
    async createLivePlanForAnchor(anchorName, appointmentTime, liveEndTime) {
        try {
            console.log(`🎯 开始为主播 ${anchorName} 创建直播计划`);

            // 获取主播cookie
            const anchor = await database.get(
                'SELECT anchor_cookie FROM anchors WHERE anchor_name = ? AND status = ? AND anchor_cookie IS NOT NULL AND anchor_cookie != ?',
                [anchorName, 'active', '']
            );

            if (!anchor || !anchor.anchor_cookie) {
                throw new Error(`主播 ${anchorName} 的cookie不存在或无效`);
            }

            // 提取h5Token
            const h5Token = extractH5Token(anchor.anchor_cookie);
            if (!h5Token) {
                throw new Error(`主播 ${anchorName} 的cookie中缺少有效的h5Token`);
            }

            console.log(`🔑 主播 ${anchorName} h5Token: ${h5Token.substring(0, 10)}...`);

            // 1. 获取直播间列表
            const roomListResult = await this.getRoomList(h5Token, anchor.anchor_cookie);
            if (!roomListResult.success) {
                throw new Error(`获取直播间列表失败: ${roomListResult.error}`);
            }

            console.log(`🏠 获取到直播间号: ${roomListResult.roomNum}`);

            // 2. 查询直播间信息
            const roomInfoResult = await this.queryRoomInfo(h5Token, anchor.anchor_cookie, roomListResult.roomNum);
            if (!roomInfoResult.success) {
                throw new Error(`查询直播间信息失败: ${roomInfoResult.error}`);
            }

            console.log(`📋 获取到直播间信息: ${JSON.stringify(roomInfoResult.roomInfo, null, 2)}`);

            // 3. 创建直播计划
            const createResult = await this.createLivePlan(
                h5Token, 
                anchor.anchor_cookie, 
                roomListResult.roomNum, 
                roomInfoResult.roomInfo, 
                appointmentTime, 
                liveEndTime
            );

            if (!createResult.success) {
                throw new Error(`创建直播计划失败: ${createResult.error}`);
            }

            console.log(`✅ 主播 ${anchorName} 直播计划创建成功，liveId: ${createResult.liveId}`);

            return {
                success: true,
                liveId: createResult.liveId,
                anchorName: anchorName
            };

        } catch (error) {
            console.error(`❌ 为主播 ${anchorName} 创建直播计划失败:`, error);
            return {
                success: false,
                error: error.message,
                anchorName: anchorName
            };
        }
    }
}

export { LivePlanCreator };
