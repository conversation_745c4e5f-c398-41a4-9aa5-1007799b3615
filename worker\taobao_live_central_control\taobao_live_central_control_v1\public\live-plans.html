<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直播计划管理</title>

    <script src="https://registry.npmmirror.com/tailwindcss-cdn/3.4.10/files/tailwindcss.js"></script>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://fast-**********.cos.ap-shanghai.myqcloud.com/static_file/js/xlsx.full.min.js" defer></script>
    <script src="https://fast-**********.cos.ap-shanghai.myqcloud.com/static_file/js/jquery-3.5.0.min.js"></script>
    <script src="https://fast-**********.cos.ap-shanghai.myqcloud.com/static_file/js/layer.3.5.1/layer.js"></script>
    <script src="js/common-utils.js"></script>
    <script src="js/product-list-modal.js" defer></script>
    <script src="js/gpu-accounts.js" defer></script>
    <script src="js/create-live-plan.js" defer></script>
    <style>
        .table-container {
            overflow-x: auto;
        }

        @media (max-width: 640px) {
            .pagination-desktop {
                display: none;
            }

            .pagination-mobile {
                display: flex;
            }
        }

        @media (min-width: 641px) {
            .pagination-desktop {
                display: flex;
            }

            .pagination-mobile {
                display: none;
            }
        }

        .stat-card {
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .sort-icon {
            display: inline-block;
            width: 8px;
            height: 8px;
            margin-left: 5px;
        }

        .sort-asc::after {
            content: '▲';
            font-size: 10px;
        }

        .sort-desc::after {
            content: '▼';
            font-size: 10px;
        }

        th.sortable {
            cursor: pointer;
        }

        th.sortable:hover {
            background-color: #f3f4f6;
        }

        .content-width {
            width: 80%;
        }

        .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-scheduled {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .status-live {
            background-color: #dcfce7;
            color: #166534;
        }

        .status-ended {
            background-color: #f3f4f6;
            color: #374151;
        }

        .status-cancelled {
            background-color: #fee2e2;
            color: #dc2626;
        }

        /* 主播按钮样式 */
        .anchor-btn {
            padding: 6px 16px;
            border: 2px solid #d1d5db;
            background-color: #ffffff;
            color: #374151;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .anchor-btn:hover {
            border-color: #ea580c;
            color: #ea580c;
            background-color: #fff7ed;
        }

        .anchor-btn-active {
            border-color: #ea580c;
            background-color: #ea580c;
            color: #ffffff;
        }

        .anchor-btn-active:hover {
            border-color: #dc2626;
            background-color: #dc2626;
            color: #ffffff;
        }

        /* 时间按钮样式 */
        .time-btn {
            padding: 6px 16px;
            border: 2px solid #d1d5db;
            background-color: #ffffff;
            color: #374151;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .time-btn:hover {
            border-color: #4f46e5;
            color: #4f46e5;
            background-color: #eef2ff;
        }

        .time-btn-active {
            border-color: #4f46e5;
            background-color: #4f46e5;
            color: #ffffff;
        }

        .time-btn-active:hover {
            border-color: #3730a3;
            background-color: #3730a3;
            color: #ffffff;
        }

        /* 直播状态按钮样式 */
        .status-btn {
            padding: 6px 16px;
            border: 2px solid #d1d5db;
            background-color: #ffffff;
            color: #374151;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .status-btn:hover {
            border-color: #10b981;
            color: #10b981;
            background-color: #ecfdf5;
        }

        .status-btn-active {
            border-color: #10b981;
            background-color: #10b981;
            color: #ffffff;
        }

        .status-btn-active:hover {
            border-color: #059669;
            background-color: #059669;
            color: #ffffff;
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-orange-600 shadow">
        <div class="content-width mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <span class="text-xl font-bold text-white">直播计划管理</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="live-plans.html" class="bg-orange-700 text-white px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-video mr-1"></i>直播计划
                    </a>
                    <a href="task-manager.html" class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-tasks mr-1"></i>任务管理
                    </a>
                    <a href="gpu-accounts.html" class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-server mr-1"></i>GPU账号
                    </a>
                    <a href="anchors.html" class="text-white hover:text-orange-200 px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-microphone mr-1"></i>主播管理
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <main class="content-width mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 筛选表单 -->
        <div class="bg-white shadow rounded-lg mb-6 p-4">
            <form id="filterForm" class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-6">
                <!-- 主播筛选 -->
                <div class="sm:col-span-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">主播筛选</label>
                    <div id="anchorButtons" class="flex flex-wrap gap-2">
                        <button type="button" class="anchor-btn anchor-btn-active" data-anchor="">
                            全部
                        </button>
                        <!-- 主播按钮将通过JavaScript动态生成 -->
                    </div>
                    <input type="hidden" id="anchorFilter" name="anchor" value="">
                </div>

                <!-- 直播状态筛选 -->
                <div class="sm:col-span-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">直播状态筛选</label>
                    <div id="statusButtons" class="flex flex-wrap gap-2">
                        <button type="button" class="status-btn status-btn-active" data-status="">
                            全部
                        </button>
                        <button type="button" class="status-btn" data-status="直播中">
                            直播中
                        </button>
                        <button type="button" class="status-btn" data-status="未开播">
                            未开播
                        </button>
                        <button type="button" class="status-btn" data-status="已结束">
                            已结束
                        </button>
                    </div>
                    <input type="hidden" id="statusFilter" name="liveStatus" value="">
                </div>

                <!-- 直播日期 -->
                <div class="sm:col-span-3">
                    <label for="liveDate" class="block text-sm font-medium text-gray-700">直播日期</label>
                    <div class="mt-1">
                        <input type="date" id="liveDate" name="liveDate"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                    </div>
                </div>

                <!-- 直播ID -->
                <div class="sm:col-span-2">
                    <label for="liveIdFilter" class="block text-sm font-medium text-gray-700">直播ID</label>
                    <div class="mt-1">
                        <input type="text" id="liveIdFilter" name="liveId" placeholder="输入直播ID"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                    </div>
                </div>

                <!-- 视频状态 -->
                <div class="sm:col-span-1">
                    <label for="videoStatusFilter" class="block text-sm font-medium text-gray-700">视频状态</label>
                    <div class="mt-1">
                        <select id="videoStatusFilter" name="validStatus"
                            class="shadow-sm focus:ring-orange-500 focus:border-orange-500 block w-full sm:text-sm border-gray-300 rounded-md p-2 border">
                            <option value="">全部</option>
                            <option value="已展示">已展示</option>
                            <option value="已隐藏">已隐藏</option>
                        </select>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="sm:col-span-6 flex justify-between items-center">
                    <div class="flex items-center">
                        <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                            <i class="fas fa-filter mr-2"></i> 查询
                        </button>
                        <button type="button" id="resetBtn"
                            class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                            <i class="fas fa-redo mr-2"></i> 重置
                        </button>
                        <button type="button" id="syncBtn"
                            class="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-sync mr-2"></i> 同步直播计划
                        </button>
                        <button type="button" id="syncAllBtn"
                            class="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <i class="fas fa-sync-alt mr-2"></i> 同步所有直播计划
                        </button>
                        <button type="button" id="extractAllProductsBtn"
                            class="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                            <i class="fas fa-download mr-2"></i> 一键提取产品
                        </button>
                        <button type="button" id="createLivePlanBtn"
                            class="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <i class="fas fa-plus mr-2"></i> 创建直播计划
                        </button>
                        <button type="button" id="hideAllLivesBtn"
                            class="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-eye mr-2"></i> 展示隐藏场次
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 数据概览 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-blue-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-video text-blue-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">总直播数</p>
                        <p class="text-2xl font-semibold text-gray-700" id="totalLives">--</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-green-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-play-circle text-green-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">进行中</p>
                        <p class="text-2xl font-semibold text-gray-700" id="liveLives">--</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-yellow-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-clock text-yellow-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">已预约</p>
                        <p class="text-2xl font-semibold text-gray-700" id="scheduledLives">--</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 stat-card">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-purple-100 mr-4 flex items-center justify-center">
                        <i class="fas fa-money-bill-wave text-purple-500"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 text-sm">总销售额</p>
                        <p class="text-2xl font-semibold text-gray-700" id="totalSales">--</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="bg-white shadow overflow-hidden rounded-lg">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">直播计划列表</h3>
                <div class="flex items-center">
                    <div class="text-sm text-gray-500 mr-4">共 <span id="totalCount">0</span> 条记录</div>
                </div>
            </div>
            <div class="border-t border-gray-200 table-container">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="toggleSort('live_id')">
                                直播ID <span id="sort-live_id" class="sort-icon"></span>
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                主播名称
                            </th>
                            <!-- <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                直播标题
                            </th> -->
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                直播类目
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="toggleSort('live_date')">
                                日期 <span id="sort-live_date" class="sort-icon"></span>
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                预约时间
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                开播时间
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                直播状态
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                视频状态
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="toggleSort('product_count')">
                                产品数量 <span id="sort-product_count" class="sort-icon"></span>
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="toggleSort('average_amount')">
                                平均价格 <span id="sort-average_amount" class="sort-icon"></span>
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="toggleSort('average_commission')">
                                平均佣金 <span id="sort-average_commission" class="sort-icon"></span>
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="toggleSort('total_sales')">
                                销售额 <span id="sort-total_sales" class="sort-icon"></span>
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="toggleSort('total_commission')">
                                佣金额 <span id="sort-total_commission" class="sort-icon"></span>
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onclick="toggleSort('created_at')">
                                创建时间 <span id="sort-created_at" class="sort-icon"></span>
                            </th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody id="livePlansTable" class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="16" class="px-6 py-4 text-center text-sm text-gray-500">加载中...</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <!-- 移动端分页 -->
                <div class="pagination-mobile flex-1 flex justify-between">
                    <button id="prevPageMobile"
                        class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        上一页
                    </button>
                    <div class="text-sm text-gray-700 flex items-center">
                        第 <span id="currentPageMobile" class="mx-1">1</span> / <span id="totalPagesMobile" class="mx-1">1</span> 页
                    </div>
                    <button id="nextPageMobile"
                        class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        下一页
                    </button>
                </div>
                <!-- 桌面端分页 -->
                <div class="pagination-desktop flex-1 sm:flex sm:items-center sm:justify-between ml-4">
                    <div>
                        <p class="text-sm text-gray-700">
                            显示第 <span id="startItem" class="font-medium">1</span> 到第
                            <span id="endItem" class="font-medium">10</span> 条，共
                            <span id="totalItems" class="font-medium">0</span> 条
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination" id="pagination">
                            <!-- 分页按钮将通过JavaScript填充 -->
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 访问密码输入弹窗 -->
    <div id="apiKeyModal" class="fixed z-10 inset-0 overflow-y-auto hidden">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                请输入访问密码
                            </h3>
                            <div class="mt-4">
                                <form id="apiKeyForm">
                                    <div class="mb-4">
                                        <label for="apiKey" class="block text-sm font-medium text-gray-700">访问密码</label>
                                        <input type="password" name="apiKey" id="apiKey"
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                                            required>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button id="submitApiKeyBtn" type="button"
                        class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-orange-600 text-base font-medium text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 sm:ml-3 sm:w-auto sm:text-sm">
                        确认
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 同步直播计划弹窗 -->
    <div id="syncModal" class="fixed z-10 inset-0 overflow-y-auto hidden">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="sync-modal-title">
                                同步直播计划
                            </h3>
                            <div class="mt-4">
                                <form id="syncForm">
                                    <div class="mb-4">
                                        <label for="syncAnchor" class="block text-sm font-medium text-gray-700">选择主播</label>
                                        <select id="syncAnchor" name="syncAnchor" required
                                            class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm">
                                            <option value="">请选择主播</option>
                                        </select>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button id="submitSyncBtn" type="button"
                        class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                        开始同步
                    </button>
                    <button id="cancelSyncBtn" type="button"
                        class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 一键提取产品选择弹窗 -->
    <div id="extractProductsModal" class="fixed z-10 inset-0 overflow-y-auto hidden">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">
                                选择产品提取范围
                            </h3>
                            <div class="mt-4">
                                <p class="text-sm text-gray-500 mb-4">请选择要提取产品的直播范围：</p>
                                <div class="space-y-3">
                                    <button id="extractTodayBtn" type="button"
                                        class="w-full inline-flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                        <i class="fas fa-calendar-day mr-2"></i>
                                        同步今日直播
                                        <span class="ml-2 text-xs text-green-200">(今天的所有直播)</span>
                                    </button>
                                    <button id="extractZeroProductsBtn" type="button"
                                        class="w-full inline-flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                                        <i class="fas fa-exclamation-triangle mr-2"></i>
                                        同步产品数量为0的直播
                                        <span class="ml-2 text-xs text-orange-200">(补充缺失产品)</span>
                                    </button>
                                    <button id="extractRecentBtn" type="button"
                                        class="w-full inline-flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                                        <i class="fas fa-history mr-2"></i>
                                        同步3天内直播
                                        <span class="ml-2 text-xs text-purple-200">(最近3天的所有直播)</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button id="cancelExtractBtn" type="button"
                        class="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 sm:w-auto sm:text-sm">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 当前页码和每页显示数量
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 0;
        let filters = {};
        let currentSortField = 'live_date';
        let currentSortOrder = 'DESC';
        let searchTimeout = null;

        // 页面加载完成后获取数据
        $(document).ready(function () {
            if (typeof layer === 'undefined') {
                console.error('layer.js 未正确加载');
                return;
            }

            initEventListeners();

            // 检查是否有访问密码，如果没有则显示输入弹窗
            const apiKey = getCookie('api_key');
            if (!apiKey) {
                showApiKeyModal();
                return;
            }

            // 设置默认排序图标
            $(`#sort-${currentSortField}`).addClass(currentSortOrder === 'ASC' ? 'sort-asc' : 'sort-desc');

            // 绑定"全部"按钮点击事件
            bindAnchorButtonEvents();
            bindStatusButtonEvents();

            // 并行加载数据以提高性能
            Promise.all([
                loadAnchorNames(),
                loadLivePlansAndStats()
            ]).catch(error => {
                console.error('初始化数据加载失败:', error);
            });
        });

        // 初始化事件监听器
        function initEventListeners() {
            // 筛选表单提交
            $('#filterForm').on('submit', function (e) {
                e.preventDefault();
                currentPage = 1;
                loadLivePlans();
            });

            // 重置按钮
            $('#resetBtn').on('click', function () {
                $('#filterForm')[0].reset();

                // 重置主播按钮状态
                $('.anchor-btn').removeClass('anchor-btn-active');
                $('.anchor-btn[data-anchor=""]').addClass('anchor-btn-active');
                $('#anchorFilter').val('');

                // 重置直播状态按钮状态
                $('.status-btn').removeClass('status-btn-active');
                $('.status-btn[data-status=""]').addClass('status-btn-active');
                $('#statusFilter').val('');

                currentPage = 1;
                loadLivePlans();
            });

            // 同步按钮
            $('#syncBtn').on('click', function () {
                showSyncModal();
            });

            // 同步所有主播按钮
            $('#syncAllBtn').on('click', function () {
                syncAllAnchors();
            });

            // 一键提取产品按钮
            $('#extractAllProductsBtn').on('click', function () {
                showExtractProductsModal();
            });

            // 创建直播计划按钮
            $('#createLivePlanBtn').on('click', function () {
                showCreateLivePlanModal();
            });

            // 展示隐藏场次按钮
            $('#hideAllLivesBtn').on('click', function () {
                showHideLivesModal();
            });

            // 操作类型切换事件
            $(document).on('change', 'input[name="operationType"]', function() {
                updateOperationUI();
            });

            // 移动端分页按钮
            $('#prevPageMobile').on('click', function () {
                if (currentPage > 1) {
                    currentPage--;
                    loadLivePlans();
                }
            });

            $('#nextPageMobile').on('click', function () {
                if (currentPage < totalPages) {
                    currentPage++;
                    loadLivePlans();
                }
            });

            // 访问密码提交按钮
            $('#submitApiKeyBtn').on('click', function () {
                submitApiKey();
            });

            // 访问密码表单回车提交
            $('#apiKeyForm').on('submit', function (e) {
                e.preventDefault();
                submitApiKey();
            });

            // 主播按钮点击事件（将在loadAnchorNames中绑定）

            $('#liveDate').on('change', function () {
                debouncedSearch();
            });

            $('#liveIdFilter').on('input', function () {
                debouncedSearch();
            });

            $('#videoStatusFilter').on('change', function () {
                debouncedSearch();
            });

            // 同步弹窗相关事件
            $('#submitSyncBtn').on('click', function () {
                submitSync();
            });

            $('#cancelSyncBtn').on('click', function () {
                hideSyncModal();
            });

            $('#syncForm').on('submit', function (e) {
                e.preventDefault();
                submitSync();
            });

            // 一键提取产品弹窗相关事件
            $('#extractTodayBtn').on('click', function () {
                extractProductsByType('today');
            });

            $('#extractZeroProductsBtn').on('click', function () {
                extractProductsByType('zero');
            });

            $('#extractRecentBtn').on('click', function () {
                extractProductsByType('recent');
            });

            $('#cancelExtractBtn').on('click', function () {
                hideExtractProductsModal();
            });

            // 绑定手卡modal的单选按钮事件
            bindHandCardModalEvents();
        }

        // 绑定手卡modal的事件
        function bindHandCardModalEvents() {
            // 手卡类型按钮点击事件
            $(document).on('click', '.handcard-type-btn', function() {
                // 移除所有选中状态
                $('.handcard-type-btn').removeClass('bg-purple-100 border-purple-300 text-purple-700')
                                       .addClass('bg-gray-100 border-gray-300 text-gray-700');

                // 添加当前选中状态
                $(this).removeClass('bg-gray-100 border-gray-300 text-gray-700')
                       .addClass('bg-purple-100 border-purple-300 text-purple-700');

                // 选中对应的radio
                $(this).prev('input[type="radio"]').prop('checked', true);
            });

            // 提取类型按钮点击事件
            $(document).on('click', '.extract-type-btn', function() {
                // 移除所有选中状态
                $('.extract-type-btn').removeClass('bg-blue-100 border-blue-300 text-blue-700')
                                      .addClass('bg-gray-100 border-gray-300 text-gray-700');

                // 添加当前选中状态
                $(this).removeClass('bg-gray-100 border-gray-300 text-gray-700')
                       .addClass('bg-blue-100 border-blue-300 text-blue-700');

                // 选中对应的radio
                $(this).prev('input[type="radio"]').prop('checked', true);
            });
        }

        // 防抖查询函数
        function debouncedSearch() {
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
            searchTimeout = setTimeout(function () {
                currentPage = 1;
                loadLivePlans();
            }, 300);
        }

        // 加载主播名称列表
        async function loadAnchorNames() {
            try {
                const response = await fetch("/api/anchors?mode=full", {
                    headers: addApiKeyHeader()
                });

                if (response.status === 401) {
                    const data = await response.json();
                    if (data.error === "invalid_api_key") {
                        showApiKeyModal();
                        return;
                    }
                }

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '获取主播列表失败');
                }

                // 更新同步弹窗的主播选择下拉框
                const syncAnchorSelect = $('#syncAnchor');
                syncAnchorSelect.empty().append('<option value="">请选择主播</option>');

                if (data.anchors && data.anchors.length > 0) {
                    // 按sort字段正序排列主播
                    const sortedAnchors = [...data.anchors].sort((a, b) => {
                        const sortA = parseInt(a.sort) || 0;
                        const sortB = parseInt(b.sort) || 0;
                        return sortA - sortB;
                    });

                    // 生成主播按钮
                    const anchorButtonsContainer = $('#anchorButtons');
                    
                    // 清空容器，但保留"全部"按钮
                    anchorButtonsContainer.find('.anchor-btn:not([data-anchor=""])').remove();

                    // 添加主播按钮
                    sortedAnchors.forEach(anchor => {
                        const button = $(`
                            <button type="button" class="anchor-btn" data-anchor="${anchor.anchor_name}">
                                ${anchor.anchor_name}
                            </button>
                        `);
                        anchorButtonsContainer.append(button);
                        
                        // 同时更新同步弹窗的下拉框
                        syncAnchorSelect.append(`<option value="${anchor.anchor_name}">${anchor.anchor_name}</option>`);
                    });

                    // 绑定按钮点击事件
                    bindAnchorButtonEvents();
                }
            } catch (error) {
                console.error('加载主播列表失败:', error);
                layer.msg('加载主播列表失败: ' + error.message, { icon: 2 });
            }
        }

        // 绑定主播按钮点击事件
        function bindAnchorButtonEvents() {
            $('.anchor-btn').off('click').on('click', function() {
                const anchorName = $(this).data('anchor');

                // 更新按钮状态
                $('.anchor-btn').removeClass('anchor-btn-active');
                $(this).addClass('anchor-btn-active');

                // 更新隐藏的筛选字段
                $('#anchorFilter').val(anchorName);

                // 触发查询
                currentPage = 1;
                loadLivePlans();
            });
        }

        // 绑定直播状态按钮点击事件
        function bindStatusButtonEvents() {
            $('.status-btn').off('click').on('click', function() {
                const statusName = $(this).data('status');

                // 更新按钮状态
                $('.status-btn').removeClass('status-btn-active');
                $(this).addClass('status-btn-active');

                // 更新隐藏的筛选字段
                $('#statusFilter').val(statusName);

                // 触发查询
                currentPage = 1;
                loadLivePlans();
            });
        }

        // 加载直播计划数据和统计信息
        async function loadLivePlansAndStats() {
            try {
                await Promise.all([
                    loadLivePlans(),
                    loadLivePlansStats()
                ]);
            } catch (error) {
                console.error('加载数据失败:', error);
                layer.msg('加载数据失败: ' + error.message, { icon: 2 });
            }
        }

        // 仅加载直播计划数据
        async function loadLivePlans() {
            try {
                showLoadingState();

                const formData = getFilterParams();
                const params = new URLSearchParams(formData);

                params.append('page', currentPage);
                params.append('limit', pageSize);
                params.append('sortField', currentSortField);
                params.append('sortOrder', currentSortOrder);

                const response = await fetch(`/api/live-plans?${params.toString()}`, {
                    headers: addApiKeyHeader()
                });

                if (response.status === 401) {
                    const data = await response.json();
                    if (data.error === "invalid_api_key") {
                        showApiKeyModal();
                        return;
                    }
                }

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '获取直播计划数据失败');
                }

                updateLivePlansTable(data.livePlans || []);
                updatePagination(data.pagination || {});
                updateTotalCount(data.pagination?.total || 0);

            } catch (error) {
                console.error('加载直播计划数据失败:', error);
                showErrorState('加载数据失败: ' + error.message);
            }
        }

        // 加载统计信息
        async function loadLivePlansStats() {
            try {
                const formData = getFilterParams();
                const params = new URLSearchParams(formData);

                const response = await fetch(`/api/live-plans/stats?${params.toString()}`, {
                    headers: addApiKeyHeader()
                });

                if (response.status === 401) {
                    const data = await response.json();
                    if (data.error === "invalid_api_key") {
                        showApiKeyModal();
                        return;
                    }
                }

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '获取统计数据失败');
                }

                updateStatsCards(data.stats || {});

            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }

        // 获取筛选参数
        function getFilterParams() {
            const formData = new FormData(document.getElementById('filterForm'));
            const params = {};

            for (let [key, value] of formData.entries()) {
                if (value && value.trim() !== '') {
                    params[key] = value.trim();
                }
            }

            return params;
        }

        // 更新直播计划表格
        function updateLivePlansTable(livePlans) {
            const tbody = $('#livePlansTable');
            tbody.empty();

            if (!livePlans || livePlans.length === 0) {
                tbody.append(`
                    <tr>
                        <td colspan="16" class="px-6 py-4 text-center text-sm text-gray-500">暂无数据</td>
                    </tr>
                `);
                return;
            }

            livePlans.forEach(plan => {
                const statusClass = getStatusClass(plan.live_status);
                const statusText = getStatusText(plan.live_status);

                tbody.append(`
                    <tr class="hover:bg-gray-50">
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${plan.live_id || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${plan.anchor_name || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${plan.live_category || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${plan.live_date || '--'}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${formatDateTime(plan.scheduled_time)}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${formatDateTime(plan.start_time)}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                            <span class="status-badge ${statusClass}">${statusText}</span>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                            <span class="status-badge ${getVideoStatusClass(plan.valid_status)}">${getVideoStatusText(plan.valid_status)}</span>
                        </td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${plan.product_count || 0}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">¥${formatNumber(plan.average_amount || 0)}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">¥${formatNumber(plan.average_commission || 0)}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">¥${formatNumber(plan.total_sales || 0)}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">¥${formatNumber(plan.total_commission || 0)}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-900">${formatDateTime(plan.created_at)}</td>
                        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                            <button onclick="showProductList('${plan.live_id}', '${plan.anchor_name || ''}')"
                                    class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-2">
                                <i class="fas fa-list mr-1"></i>详情
                            </button>
                           
                        </td>
                    </tr>
                `);
            });
        }
//  <button onclick="extractProducts('${plan.live_id}')"
//                                     class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
//                                 <i class="fas fa-download mr-1"></i>提取产品
//                             </button>
//  <button onclick="createHandCardTask('${plan.live_id}', '${plan.anchor_name}', '${plan.live_title}')"
//                                     class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
//                                 <i class="fas fa-hand-paper mr-1"></i>提取手卡任务
//                             </button>
        // 更新统计卡片
        function updateStatsCards(stats) {
            $('#totalLives').text(formatNumber(stats.totalLives || 0));
            $('#liveLives').text(formatNumber(stats.liveLives || 0));
            $('#scheduledLives').text(formatNumber(stats.scheduledLives || 0));
            $('#totalSales').text('¥' + formatNumber(stats.totalSales || 0));
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case '未开播': return 'status-scheduled';
                case '直播中': return 'status-live';
                case '已结束': return 'status-ended';
                case '已取消': return 'status-cancelled';
                default: return 'status-scheduled';
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            return status || '未知';
        }

        // 获取视频状态样式类
        function getVideoStatusClass(validStatus) {
            switch (validStatus) {
                case '已展示': return 'status-live'; // 已展示 - 绿色
                case '已隐藏': return 'status-cancelled'; // 已隐藏 - 红色
                default: return 'status-live'; // 默认为已展示
            }
        }

        // 获取视频状态文本
        function getVideoStatusText(validStatus) {
            switch (validStatus) {
                case '已展示': return '已展示';
                case '已隐藏': return '已隐藏';
                default: return '已展示';
            }
        }

        // 显示加载状态
        function showLoadingState() {
            $('#livePlansTable').html(`
                <tr>
                    <td colspan="16" class="px-6 py-4 text-center text-sm text-gray-500">
                        <i class="fas fa-spinner fa-spin mr-2"></i>加载中...
                    </td>
                </tr>
            `);
        }

        // 显示错误状态
        function showErrorState(message) {
            $('#livePlansTable').html(`
                <tr>
                    <td colspan="16" class="px-6 py-4 text-center text-sm text-red-500">
                        <i class="fas fa-exclamation-triangle mr-2"></i>${message}
                    </td>
                </tr>
            `);
        }

        // 更新总数显示
        function updateTotalCount(total) {
            $('#totalCount').text(formatNumber(total));
        }

        // 排序功能
        function toggleSort(field) {
            if (currentSortField === field) {
                currentSortOrder = currentSortOrder === 'ASC' ? 'DESC' : 'ASC';
            } else {
                currentSortField = field;
                currentSortOrder = 'DESC';
            }

            // 更新排序图标
            $('.sort-icon').removeClass('sort-asc sort-desc');
            $(`#sort-${field}`).addClass(currentSortOrder === 'ASC' ? 'sort-asc' : 'sort-desc');

            // 重新加载数据
            currentPage = 1;
            loadLivePlans();
        }

        // 更新分页
        function updatePagination(pagination) {
            totalPages = pagination.totalPages || 1;
            const total = pagination.total || 0;
            const page = pagination.page || 1;
            const limit = pagination.limit || 10;

            // 更新移动端分页
            $('#currentPageMobile').text(page);
            $('#totalPagesMobile').text(totalPages);

            // 更新桌面端分页信息
            const startItem = total === 0 ? 0 : (page - 1) * limit + 1;
            const endItem = Math.min(page * limit, total);

            $('#startItem').text(startItem);
            $('#endItem').text(endItem);
            $('#totalItems').text(total);

            // 更新分页按钮状态
            $('#prevPageMobile').prop('disabled', page <= 1);
            $('#nextPageMobile').prop('disabled', page >= totalPages);

            // 生成桌面端分页按钮
            generatePaginationButtons(page, totalPages);
        }

        // 生成分页按钮
        function generatePaginationButtons(currentPage, totalPages) {
            const pagination = $('#pagination');
            pagination.empty();

            if (totalPages <= 1) return;

            // 上一页按钮
            const prevDisabled = currentPage <= 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50';
            pagination.append(`
                <button onclick="${currentPage > 1 ? 'changePage(' + (currentPage - 1) + ')' : ''}"
                        class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 ${prevDisabled}">
                    <i class="fas fa-chevron-left"></i>
                </button>
            `);

            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            if (startPage > 1) {
                pagination.append(`
                    <button onclick="changePage(1)" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">1</button>
                `);
                if (startPage > 2) {
                    pagination.append(`<span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>`);
                }
            }

            for (let i = startPage; i <= endPage; i++) {
                const isActive = i === currentPage;
                const buttonClass = isActive
                    ? 'relative inline-flex items-center px-4 py-2 border border-orange-500 bg-orange-50 text-sm font-medium text-orange-600'
                    : 'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50';

                pagination.append(`
                    <button onclick="${isActive ? '' : 'changePage(' + i + ')'}" class="${buttonClass}">${i}</button>
                `);
            }

            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    pagination.append(`<span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>`);
                }
                pagination.append(`
                    <button onclick="changePage(${totalPages})" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">${totalPages}</button>
                `);
            }

            // 下一页按钮
            const nextDisabled = currentPage >= totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50';
            pagination.append(`
                <button onclick="${currentPage < totalPages ? 'changePage(' + (currentPage + 1) + ')' : ''}"
                        class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 ${nextDisabled}">
                    <i class="fas fa-chevron-right"></i>
                </button>
            `);
        }

        // 切换页面
        function changePage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPage) {
                currentPage = page;
                loadLivePlans();
            }
        }

        // 工具函数
        function formatNumber(num) {
            if (num === null || num === undefined) return '0';
            return parseFloat(num).toLocaleString('zh-CN', { minimumFractionDigits: 0, maximumFractionDigits: 2 });
        }

        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '--';
            try {
                const date = new Date(dateTimeStr);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (error) {
                return dateTimeStr;
            }
        }

        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }

        function addApiKeyHeader() {
            const apiKey = getCookie('api_key');
            return apiKey ? { 'X-API-Key': apiKey } : {};
        }

        function showApiKeyModal() {
            $('#apiKeyModal').removeClass('hidden');
        }

        function hideApiKeyModal() {
            $('#apiKeyModal').addClass('hidden');
        }

        function submitApiKey() {
            const apiKey = $('#apiKey').val().trim();
            if (!apiKey) {
                layer.msg('请输入访问密码', { icon: 2 });
                return;
            }

            // 设置cookie
            document.cookie = `api_key=${apiKey}; path=/; max-age=86400`;

            hideApiKeyModal();

            // 重新加载数据
            Promise.all([
                loadAnchorNames(),
                loadLivePlansAndStats()
            ]).catch(error => {
                console.error('初始化数据加载失败:', error);
                layer.msg('加载数据失败: ' + error.message, { icon: 2 });
            });
        }

        // 显示同步弹窗
        function showSyncModal() {
            // 如果有筛选的主播，自动选中
            const selectedAnchor = $('#anchorFilter').val();
            if (selectedAnchor) {
                $('#syncAnchor').val(selectedAnchor);
            }
            $('#syncModal').removeClass('hidden');
            $('#syncAnchor').focus();
        }

        // 隐藏同步弹窗
        function hideSyncModal() {
            $('#syncModal').addClass('hidden');
            $('#syncForm')[0].reset();
        }

        // 提交同步
        async function submitSync() {
            const anchorName = $('#syncAnchor').val().trim();

            if (!anchorName) {
                layer.msg('请选择主播', { icon: 2 });
                return;
            }

            // 显示加载状态
            const loadingIndex = layer.msg('正在同步直播计划...', {
                icon: 16,
                shade: 0.3,
                time: 0
            });

            try {
                const response = await fetch('/api/live-plans/sync', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...addApiKeyHeader()
                    },
                    body: JSON.stringify({
                        anchorName: anchorName
                    })
                });

                layer.close(loadingIndex);

                if (response.status === 401) {
                    const data = await response.json();
                    if (data.error === "invalid_api_key") {
                        showApiKeyModal();
                        return;
                    }
                }

                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.message || '同步失败');
                }

                if (result.success) {
                    const { inserted, updated, deleted, total } = result.data;
                    let message = `同步完成！新增 ${inserted} 条，更新 ${updated} 条`;
                    if (deleted > 0) {
                        message += `，删除 ${deleted} 条`;
                    }
                    message += `，共处理 ${total} 条记录`;

                    layer.msg(message, {
                        icon: 1,
                        time: 3000
                    });

                    // 隐藏弹窗并刷新数据
                    hideSyncModal();
                    loadLivePlansAndStats();
                } else {
                    layer.msg('同步失败: ' + result.error, { icon: 2 });
                }

            } catch (error) {
                layer.close(loadingIndex);
                console.error('同步失败:', error);
                layer.msg('同步失败: ' + error.message, { icon: 2 });
            }
        }

        // 提取产品函数
        async function extractProducts(liveId) {
            if (!liveId) {
                layer.msg('直播ID不能为空', { icon: 2 });
                return;
            }

            // 显示确认对话框
            layer.confirm('确定要提取该直播的产品吗？', {
                icon: 3,
                title: '确认提取'
            }, async function(index) {
                layer.close(index);

                // 显示加载状态
                const loadingIndex = layer.msg('正在提取产品...', {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                try {
                    const response = await fetch('/api/live-products/sync', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            ...addApiKeyHeader()
                        },
                        body: JSON.stringify({
                            liveId: liveId
                        })
                    });

                    layer.close(loadingIndex);

                    if (response.status === 401) {
                        const data = await response.json();
                        if (data.error === "invalid_api_key") {
                            showApiKeyModal();
                            return;
                        }
                    }

                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.message || '提取失败');
                    }

                    if (result.success) {
                        const { inserted, updated, deleted, total, stats } = result.data;
                        let message = `产品提取完成！共处理 ${total} 个产品，新增 ${inserted} 个，更新 ${updated} 个`;
                        if (deleted > 0) {
                            message += `，删除 ${deleted} 个`;
                        }
                        // if (stats) {
                        //     message += `<br>统计信息：产品数量 ${stats.product_count}，平均价格 ¥${stats.average_amount.toFixed(2)}，平均佣金 ¥${stats.average_commission.toFixed(2)}`;
                        // }
                        layer.msg(message, {
                            icon: 1,
                            time: 5000
                        });

                        // 刷新页面数据以显示更新后的统计信息
                        loadLivePlans();
                    } else {
                        layer.msg('产品提取失败: ' + result.error, { icon: 2 });
                    }

                } catch (error) {
                    layer.close(loadingIndex);
                    console.error('提取产品失败:', error);
                    layer.msg('提取产品失败: ' + error.message, { icon: 2 });
                }
            });
        }

        // 创建手卡提取任务
        async function createHandCardTask(liveId, anchorName, liveTitle) {
            if (!liveId) {
                layer.msg('直播ID不能为空', { icon: 2 });
                return;
            }

            // 显示参数选择弹窗
            showHandCardParamsModal('手卡提取任务参数设置', async function(params) {
                // 显示确认对话框
                layer.confirm(`确定要为直播"${liveTitle}"创建手卡提取任务吗？<br><br>该任务将获取该场次的所有产品并在后台异步执行手卡提取。`, {
                    icon: 3,
                    title: '确认创建任务',
                    area: ['400px', '200px']
                }, async function(index) {
                    layer.close(index);

                    await _doCreateHandCardTask(liveId, params);
                });
            });
        }

        // 执行创建手卡任务
        async function _doCreateHandCardTask(liveId, params) {
            // 显示加载状态
                const loadingIndex = layer.msg('正在创建手卡提取任务...', {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                try {
                    // 首先获取该直播的所有产品
                    const productsResponse = await fetch(`/api/live-products/list?liveId=${liveId}`, {
                        headers: addApiKeyHeader()
                    });

                    if (productsResponse.status === 401) {
                        const data = await productsResponse.json();
                        if (data.error === "invalid_api_key") {
                            layer.close(loadingIndex);
                            showApiKeyModal();
                            return;
                        }
                    }

                    const productsResult = await productsResponse.json();

                    if (!productsResponse.ok) {
                        throw new Error(productsResult.message || '获取产品列表失败');
                    }

                    const products = productsResult.products || [];
                    if (products.length === 0) {
                        layer.close(loadingIndex);
                        layer.msg('该直播暂无产品，请先同步产品数据', { icon: 0 });
                        return;
                    }

                    // 提取所有产品ID
                    const productIds = products.map(p => p.product_id).filter(Boolean);

                    if (productIds.length === 0) {
                        layer.close(loadingIndex);
                        layer.msg('未找到有效的产品ID', { icon: 2 });
                        return;
                    }

                    // 创建手卡提取任务（h5Token将在后端动态获取）
                    const taskResponse = await fetch('/api/task/hand-card/create', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            ...addApiKeyHeader()
                        },
                        body: JSON.stringify({
                            liveId: liveId,
                            productIds: productIds,
                            taskParams: params
                            // h5Token 将在后端从主播cookie中动态提取
                        })
                    });

                    layer.close(loadingIndex);

                    if (taskResponse.status === 401) {
                        const data = await taskResponse.json();
                        if (data.error === "invalid_api_key") {
                            showApiKeyModal();
                            return;
                        }
                    }

                    const taskResult = await taskResponse.json();

                    console.log('任务创建响应:', taskResult);

                    if (!taskResponse.ok) {
                        console.error('任务创建失败，响应状态:', taskResponse.status);
                        throw new Error(taskResult.message || taskResult.error || '创建任务失败');
                    }

                    if (taskResult.success) {
                        layer.msg(`手卡提取任务创建成功！<br>任务ID: ${taskResult.taskId}<br>产品数量: ${productIds.length}`, {
                            icon: 1,
                            time: 5000
                        });

                        // 询问是否打开任务管理页面
                        setTimeout(() => {
                            layer.confirm('任务已创建，是否打开任务管理页面查看进度？', {
                                btn: ['打开任务管理', '稍后查看']
                            }, function(index) {
                                window.open('/task-manager.html', '_blank');
                                layer.close(index);
                            });
                        }, 1000);

                    } else {
                        layer.msg('创建手卡提取任务失败: ' + taskResult.error, { icon: 2 });
                    }

                } catch (error) {
                    layer.close(loadingIndex);
                    console.error('创建手卡提取任务失败:', error);
                    layer.msg('创建手卡提取任务失败: ' + error.message, { icon: 2 });
                }
        }

        // 通用批量操作函数
        async function executeBatchOperation(endpoint, confirmMsg, loadingMsg, itemType, operationType, callback) {
            // 显示确认对话框
            layer.confirm(confirmMsg, {
                icon: 3,
                title: '确认操作'
            }, async function(index) {
                layer.close(index);

                // 显示加载状态
                const loadingIndex = layer.msg(loadingMsg, {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                try {
                    const response = await fetch(endpoint, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            ...addApiKeyHeader()
                        }
                    });

                    layer.close(loadingIndex);

                    if (response.status === 401) {
                        const data = await response.json();
                        if (data.error === "invalid_api_key") {
                            showApiKeyModal();
                            return;
                        }
                    }

                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.message || `${operationType}失败`);
                    }

                    if (result.success) {
                        // 兼容不同的响应数据结构
                        const data = result.data || result;
                        let message = '';

                        // 针对同步所有主播的特殊处理
                        if (data.totalCount !== undefined && data.successCount !== undefined) {
                            const totalCount = data.totalCount || 0;
                            const successCount = data.successCount || 0;
                            const failedCount = data.failedCount || 0;
                            const totalInserted = data.totalInserted || 0;
                            const totalUpdated = data.totalUpdated || 0;
                            const totalDeleted = data.totalDeleted || 0;

                            message = `${operationType}完成！共处理 ${totalCount} 个${itemType}，成功 ${successCount} 个`;
                            if (failedCount > 0) {
                                message += `，失败 ${failedCount} 个`;
                            }
                            if (totalInserted > 0 || totalUpdated > 0 || totalDeleted > 0) {
                                message += `<br>新增 ${totalInserted} 个计划，更新 ${totalUpdated} 个计划`;
                                if (totalDeleted > 0) {
                                    message += `，删除 ${totalDeleted} 个计划`;
                                }
                            }
                        } else {
                            // 其他批量操作的处理
                            const processed = data.processed || data.count || data.totalCount || 0;
                            const updated = data.updated || data.success_count;
                            const errors = data.errors || data.error_count || data.failedCount;

                            message = `${operationType}完成！共处理 ${processed} 个${itemType}`;
                            if (updated !== undefined && updated !== null) {
                                message += `，更新 ${updated} 个${itemType}`;
                            }
                            if (errors && errors > 0) {
                                message += `，${errors} 个${itemType}${operationType}失败`;
                            }
                        }

                        layer.msg(message, {
                            icon: 1,
                            time: 5000
                        });

                        // 执行回调函数（如果提供）
                        if (callback && typeof callback === 'function') {
                            callback();
                        }
                    } else {
                        layer.msg(`${operationType}失败: ` + result.error, { icon: 2 });
                    }

                } catch (error) {
                    layer.close(loadingIndex);
                    console.error(`${operationType}失败:`, error);
                    layer.msg(`${operationType}失败: ` + error.message, { icon: 2 });
                }
            });
        }

        // 同步所有主播直播计划
        async function syncAllAnchors() {
            await executeBatchOperation(
                '/api/live-plans/sync-all',
                '确定要同步所有主播的直播计划吗？这可能需要较长时间。',
                '正在同步所有主播的直播计划...',
                '主播',
                '同步',
                () => loadLivePlansAndStats()
            );
        }

        // 显示一键提取产品选择弹窗
        function showExtractProductsModal() {
            $('#extractProductsModal').removeClass('hidden');
        }

        // 隐藏一键提取产品选择弹窗
        function hideExtractProductsModal() {
            $('#extractProductsModal').addClass('hidden');
        }

        // 根据类型提取产品
        async function extractProductsByType(type) {
            let endpoint, confirmMsg, loadingMsg;
            
            switch (type) {
                case 'today':
                    endpoint = '/api/live-products/extract-today';
                    confirmMsg = '确定要提取今日所有直播的产品吗？';
                    loadingMsg = '正在提取今日直播产品...';
                    break;
                case 'zero':
                    endpoint = '/api/live-products/extract-zero';
                    confirmMsg = '确定要提取产品数量为0的直播产品吗？';
                    loadingMsg = '正在提取产品数量为0的直播产品...';
                    break;
                case 'recent':
                    endpoint = '/api/live-products/extract-recent';
                    confirmMsg = '确定要提取3天内所有直播的产品吗？这可能需要较长时间。';
                    loadingMsg = '正在提取3天内直播产品...';
                    break;
                default:
                    layer.msg('未知的提取类型', { icon: 2 });
                    return;
            }

            // 隐藏选择弹窗
            hideExtractProductsModal();

            // 显示确认对话框
            layer.confirm(confirmMsg, {
                icon: 3,
                title: '确认提取'
            }, async function(index) {
                layer.close(index);

                // 显示加载状态
                const loadingIndex = layer.msg(loadingMsg, {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                try {
                    const response = await fetch(endpoint, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            ...addApiKeyHeader()
                        }
                    });

                    layer.close(loadingIndex);

                    if (response.status === 401) {
                        const data = await response.json();
                        if (data.error === "invalid_api_key") {
                            showApiKeyModal();
                            return;
                        }
                    }

                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.message || '提取失败');
                    }

                    if (result.success) {
                        // 兼容不同的响应数据结构
                        const data = result.data || result;
                        const processed = data.processed || data.count || data.total || 0;
                        const updated = data.updated || data.success_count || 0;
                        const errors = data.errors || data.error_count || data.failed || 0;

                        let message = `产品提取完成！共处理 ${processed} 个直播，更新 ${updated} 个直播的产品信息`;
                        if (errors && errors > 0) {
                            message += `，${errors} 个直播提取失败`;
                        }
                        
                        layer.msg(message, {
                            icon: 1,
                            time: 5000
                        });

                        // 刷新页面数据
                        loadLivePlansAndStats();
                    } else {
                        layer.msg('产品提取失败: ' + result.error, { icon: 2 });
                    }

                } catch (error) {
                    layer.close(loadingIndex);
                    console.error('提取产品失败:', error);
                    layer.msg('提取产品失败: ' + error.message, { icon: 2 });
                }
            });
        }

        // 一键提取产品（提取更新三天内的所有直播计划产品）- 保留原有函数作为备用
        async function extractAllProducts() {
            await executeBatchOperation(
                '/api/live-products/extract-all',
                '确定要提取更新三天内的所有直播计划产品吗？这可能需要较长时间。',
                '正在提取所有直播计划产品...',
                '直播',
                '产品提取',
                () => loadLivePlansAndStats()
            );
        }


        // 手卡参数相关功能
        function showHandCardParamsModal(title, callback) {
            $('#handCardParamsModalTitle').text(title);

            // 重置表单状态
            $('input[name="handCardType"][value="selling_point"]').prop('checked', true);
            $('input[name="extractType"][value="new_content"]').prop('checked', true);
            $('#autoCreateAudioTask').prop('checked', true);
            $('#filterProhibitedWords').prop('checked', false);

            // 重置按钮样式
            $('.handcard-type-btn').removeClass('bg-purple-100 border-purple-300 text-purple-700')
                                   .addClass('bg-gray-100 border-gray-300 text-gray-700');
            $('input[name="handCardType"][value="selling_point"]').next('.handcard-type-btn')
                                   .removeClass('bg-gray-100 border-gray-300 text-gray-700')
                                   .addClass('bg-purple-100 border-purple-300 text-purple-700');

            $('.extract-type-btn').removeClass('bg-blue-100 border-blue-300 text-blue-700')
                                  .addClass('bg-gray-100 border-gray-300 text-gray-700');
            $('input[name="extractType"][value="new_content"]').next('.extract-type-btn')
                                  .removeClass('bg-gray-100 border-gray-300 text-gray-700')
                                  .addClass('bg-blue-100 border-blue-300 text-blue-700');

            $('#handCardParamsModal').removeClass('hidden');

            // 绑定确定按钮事件
            $('#confirmHandCardParams').off('click').on('click', () => {
                const params = getHandCardParams();
                hideHandCardParamsModal();
                if (callback) {
                    callback(params);
                }
            });
        }

        function hideHandCardParamsModal() {
            $('#handCardParamsModal').addClass('hidden');
        }

        // 展示隐藏场次相关变量
        let selectedHideAnchors = [];

        // 显示展示隐藏场次弹窗
        function showHideLivesModal() {
            // 重置选择状态
            selectedHideAnchors = [];
            console.log('显示展示隐藏场次弹窗 - 重置选择状态:', selectedHideAnchors);

            // 重置操作类型为展示
            $('input[name="operationType"][value="show"]').prop('checked', true);
            updateOperationUI();

            // 加载主播列表
            loadAnchorsForHideLives();
            // 显示弹窗
            $('#hideLivesModal').removeClass('hidden');
        }

        // 更新操作UI
        function updateOperationUI() {
            const operationType = $('input[name="operationType"]:checked').val();
            const tipDiv = $('#operationTip');
            const confirmBtn = $('#confirmHideLives');
            const confirmIcon = $('#confirmIcon');
            const confirmText = $('#confirmText');

            if (operationType === 'show') {
                // 展示模式
                tipDiv.removeClass('bg-yellow-50 border-yellow-200').addClass('bg-blue-50 border-blue-200');
                tipDiv.find('i').removeClass('text-yellow-400').addClass('text-blue-400');
                tipDiv.find('p').removeClass('text-yellow-700').addClass('text-blue-700')
                    .text('此操作将展示选中主播的所有已隐藏直播场次，操作不可撤销，请谨慎操作。');

                confirmBtn.removeClass('bg-red-600 hover:bg-red-700').addClass('bg-blue-600 hover:bg-blue-700');
                confirmIcon.removeClass('fa-eye-slash').addClass('fa-eye');
                confirmText.text('确认展示');
            } else {
                // 隐藏模式
                tipDiv.removeClass('bg-blue-50 border-blue-200').addClass('bg-yellow-50 border-yellow-200');
                tipDiv.find('i').removeClass('text-blue-400').addClass('text-yellow-400');
                tipDiv.find('p').removeClass('text-blue-700').addClass('text-yellow-700')
                    .text('此操作将隐藏选中主播的所有已展示直播场次，操作不可撤销，请谨慎操作。');

                confirmBtn.removeClass('bg-blue-600 hover:bg-blue-700').addClass('bg-red-600 hover:bg-red-700');
                confirmIcon.removeClass('fa-eye').addClass('fa-eye-slash');
                confirmText.text('确认隐藏');
            }
        }

        // 隐藏隐藏场次弹窗
        function hideHideLivesModal() {
            $('#hideLivesModal').addClass('hidden');
            // 重置选择状态
            selectedHideAnchors = [];
            $('#hideLivesAnchorButtons .anchor-btn').removeClass('anchor-btn-active');
        }

        // 加载主播列表用于隐藏场次
        async function loadAnchorsForHideLives() {
            try {
                const response = await fetch('/api/anchors?mode=full', {
                    headers: addApiKeyHeader()
                });

                if (response.status === 401) {
                    const data = await response.json();
                    if (data.error === "invalid_api_key") {
                        showApiKeyModal();
                        return;
                    }
                }

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '获取主播列表失败');
                }

                if (data.anchors && data.anchors.length > 0) {
                    // 按sort字段正序排列主播
                    const sortedAnchors = [...data.anchors].sort((a, b) => {
                        const sortA = parseInt(a.sort) || 0;
                        const sortB = parseInt(b.sort) || 0;
                        return sortA - sortB;
                    });

                    // 生成主播按钮
                    const anchorButtonsContainer = $('#hideLivesAnchorButtons');
                    anchorButtonsContainer.empty();

                    // 添加"全部"按钮
                    const allButton = $(`
                        <button type="button" class="anchor-btn" data-anchor="all">
                            全部
                        </button>
                    `);
                    anchorButtonsContainer.append(allButton);

                    // 添加主播按钮
                    sortedAnchors.forEach(anchor => {
                        const button = $(`
                            <button type="button" class="anchor-btn" data-anchor="${anchor.anchor_name}">
                                ${anchor.anchor_name}
                            </button>
                        `);
                        anchorButtonsContainer.append(button);
                    });

                    // 绑定按钮点击事件 - 使用setTimeout确保DOM更新完成
                    setTimeout(() => {
                        bindHideLivesAnchorButtonEvents();
                    }, 100);
                }
            } catch (error) {
                console.error('加载主播列表失败:', error);
                layer.msg('加载主播列表失败: ' + error.message, { icon: 2 });
            }
        }

        // 绑定隐藏场次页面的主播按钮点击事件
        function bindHideLivesAnchorButtonEvents() {
            console.log('绑定隐藏场次主播按钮事件');
            const buttons = $('#hideLivesAnchorButtons .anchor-btn');
            console.log('找到的按钮数量:', buttons.length);

            buttons.each(function(index) {
                console.log(`按钮 ${index}:`, $(this).data('anchor'), $(this).text());
            });

            buttons.off('click').on('click', function() {
                const anchorName = $(this).data('anchor');
                console.log('点击主播按钮:', anchorName);
                console.log('当前选中状态:', $(this).hasClass('anchor-btn-active'));

                if (anchorName === 'all') {
                    // 点击全部按钮
                    if ($(this).hasClass('anchor-btn-active')) {
                        // 取消全选
                        $('#hideLivesAnchorButtons .anchor-btn').removeClass('anchor-btn-active');
                        selectedHideAnchors = [];
                    } else {
                        // 全选
                        $('#hideLivesAnchorButtons .anchor-btn').addClass('anchor-btn-active');
                        selectedHideAnchors = [];
                        $('#hideLivesAnchorButtons .anchor-btn').each(function() {
                            const name = $(this).data('anchor');
                            if (name !== 'all') {
                                selectedHideAnchors.push(name);
                            }
                        });
                    }
                } else {
                    // 点击具体主播按钮
                    if ($(this).hasClass('anchor-btn-active')) {
                        // 取消选中
                        $(this).removeClass('anchor-btn-active');
                        selectedHideAnchors = selectedHideAnchors.filter(name => name !== anchorName);
                        // 取消全部按钮的选中状态
                        $('#hideLivesAnchorButtons .anchor-btn[data-anchor="all"]').removeClass('anchor-btn-active');
                    } else {
                        // 选中
                        $(this).addClass('anchor-btn-active');
                        selectedHideAnchors.push(anchorName);

                        // 检查是否所有主播都被选中
                        const totalAnchors = $('#hideLivesAnchorButtons .anchor-btn').length - 1; // 减去全部按钮
                        if (selectedHideAnchors.length === totalAnchors) {
                            $('#hideLivesAnchorButtons .anchor-btn[data-anchor="all"]').addClass('anchor-btn-active');
                        }
                    }
                }

                console.log('已选择隐藏主播:', selectedHideAnchors);
            });

            // 绑定确认隐藏按钮事件
            $('#confirmHideLives').off('click').on('click', function() {
                console.log('点击确认隐藏按钮');
                confirmHideSelectedLives();
            });
        }

        // 确认操作选中的主播场次
        async function confirmHideSelectedLives() {
            console.log('确认操作 - 当前选中的主播:', selectedHideAnchors);
            console.log('选中主播数量:', selectedHideAnchors.length);

            if (selectedHideAnchors.length === 0) {
                layer.msg('请至少选择一个主播', { icon: 2 });
                return;
            }

            // 获取操作类型
            const operationType = $('input[name="operationType"]:checked').val();
            const isShowOperation = operationType === 'show';

            // 保存选中的主播到局部变量，避免作用域问题
            const selectedAnchors = [...selectedHideAnchors];
            console.log('保存的选中主播:', selectedAnchors);
            console.log('操作类型:', operationType);

            // 显示确认对话框
            const anchorNames = selectedAnchors.join('、');
            const actionText = isShowOperation ? '展示' : '隐藏';
            const statusText = isShowOperation ? '已隐藏' : '已展示';

            layer.confirm(`确定要${actionText}以下主播的所有${statusText}直播场次吗？<br><br><strong>${anchorNames}</strong><br><br>此操作不可撤销，请谨慎操作。`, {
                icon: 3,
                title: `确认${actionText}`,
                btn: ['确定', '取消']
            }, async function(index) {
                layer.close(index);

                // 显示加载状态
                const loadingIndex = layer.msg(`正在${actionText}直播场次...`, {
                    icon: 16,
                    shade: 0.3,
                    time: 0
                });

                try {
                    console.log(`发送${actionText}请求 - 选中的主播:`, selectedAnchors);
                    const requestBody = {
                        anchorNames: selectedAnchors,
                        hide: isShowOperation ? 0 : 1  // 展示时hide=0，隐藏时hide=1
                    };
                    console.log('请求体:', requestBody);

                    const response = await fetch('/api/live-plans/hide-selected', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            ...addApiKeyHeader()
                        },
                        body: JSON.stringify(requestBody)
                    });

                    layer.close(loadingIndex);

                    if (response.status === 401) {
                        const data = await response.json();
                        if (data.error === "invalid_api_key") {
                            showApiKeyModal();
                            return;
                        }
                    }

                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.message || result.error || `${actionText}失败`);
                    }

                    // 隐藏modal
                    hideHideLivesModal();

                    // 显示成功消息
                    const countText = isShowOperation ? 'showCount' : 'hiddenCount';
                    layer.msg(`成功${actionText} ${result[countText] || result.hiddenCount || 0} 个直播场次`, {
                        icon: 1,
                        time: 3000
                    });

                    // 刷新数据
                    loadLivePlansAndStats();

                } catch (error) {
                    layer.close(loadingIndex);
                    console.error(`${actionText}直播场次失败:`, error);
                    layer.msg(`${actionText}失败: ` + error.message, { icon: 2 });
                    // 出错时也隐藏modal
                    hideHideLivesModal();
                }
            });
        }

        function getHandCardParams() {
            const handCardType = $('input[name="handCardType"]:checked').val();
            const extractType = $('input[name="extractType"]:checked').val();
            const autoCreateAudioTask = $('#autoCreateAudioTask').is(':checked');
            const filterProhibitedWords = $('#filterProhibitedWords').is(':checked');

            let type, subType, isRenew, scene;

            // 设置手卡类型参数
            if (handCardType === 'selling_point') {
                type = '1';
                subType = 1;
            } else if (handCardType === 'script') {
                type = '0';
                subType = 4;
            }

            // 设置提取类型参数
            if (extractType === 'new_content') {
                isRenew = true;
                scene = 'renew';
            } else if (extractType === 'cached_content') {
                isRenew = false;
                scene = 'normal';
            }

            return {
                type,
                subType,
                isRenew,
                scene,
                autoCreateAudioTask,
                filterProhibitedWords
            };
        }

    </script>

    <!-- 创建直播计划弹窗 -->
    <div id="createLivePlanModal" class="fixed z-50 inset-0 overflow-y-auto hidden">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">

                <!-- 弹窗头部 -->
                <div class="bg-white px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">
                            创建直播计划
                        </h3>
                        <button type="button" onclick="hideCreateLivePlanModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>

                <!-- 弹窗内容 -->
                <div class="bg-white px-6 py-4">
                    <!-- 主播选择 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">选择主播</label>
                        <div id="createPlanAnchorButtons" class="flex flex-wrap gap-2">
                            <!-- 主播按钮将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 直播时间选择 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">直播时间</label>
                        <div class="flex flex-wrap gap-2 mb-3">
                            <button type="button" class="time-btn time-btn-active" data-time="today">
                                今日
                            </button>
                            <button type="button" class="time-btn" data-time="tomorrow">
                                明日
                            </button>
                        </div>
                        <div class="text-sm text-gray-500" id="selectedTimeDisplay">
                            今日 23:55 (UTC+8)
                        </div>
                    </div>
                </div>

                <!-- 弹窗底部 -->
                <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3">
                    <button type="button" onclick="hideCreateLivePlanModal()"
                        class="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button type="button" id="confirmCreateLivePlan"
                        class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700">
                        创建计划
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 隐藏场次弹窗 -->
    <div id="hideLivesModal" class="fixed z-50 inset-0 overflow-y-auto hidden">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">

                <!-- 弹窗头部 -->
                <div class="bg-white px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">
                            展示隐藏场次
                        </h3>
                        <button type="button" onclick="hideHideLivesModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>

                <!-- 弹窗内容 -->
                <div class="bg-white px-6 py-4">
                    <!-- 操作类型选择 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">操作类型</label>
                        <div class="flex gap-4">
                            <label class="inline-flex items-center">
                                <input type="radio" name="operationType" value="show" class="form-radio h-4 w-4 text-blue-600" checked>
                                <span class="ml-2 text-sm text-gray-700">展示（将已隐藏的场次设为展示）</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="operationType" value="hide" class="form-radio h-4 w-4 text-red-600">
                                <span class="ml-2 text-sm text-gray-700">隐藏（将已展示的场次设为隐藏）</span>
                            </label>
                        </div>
                    </div>

                    <!-- 主播选择 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">选择主播</label>
                        <div id="hideLivesAnchorButtons" class="flex flex-wrap gap-2">
                            <!-- 主播按钮将通过JavaScript动态生成 -->
                        </div>
                    </div>

                    <!-- 提示信息 -->
                    <div class="mb-4">
                        <div id="operationTip" class="bg-blue-50 border border-blue-200 rounded-md p-4">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle text-blue-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-blue-700">
                                        此操作将展示选中主播的所有已隐藏直播场次，操作不可撤销，请谨慎操作。
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 弹窗底部 -->
                <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3">
                    <button type="button" onclick="hideHideLivesModal()"
                        class="inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button type="button" id="confirmHideLives"
                        class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700">
                        <i id="confirmIcon" class="fas fa-eye mr-2"></i><span id="confirmText">确认展示</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

   

</body>
</html>
