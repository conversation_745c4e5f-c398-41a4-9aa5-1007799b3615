import database from './database.js';
import LiveCardExtractor from './live-card-extract.js';
import {
    getAvailableGpuAccount,
    destroyGpuInstance,
    saveAudioFile,
    getAudioDirectoryPath,
    handleGpuInstanceDestroy,
    createGpuInstance
} from './utils/gpu-utils.js';
import fs from 'fs';
import path from 'path';

/**
 * 异步任务管理器
 * 负责创建、执行、监控异步任务
 */
class TaskManager {
    constructor() {
        // 移除全局的 isProcessing 锁，改为任务类型特定的锁
        this.processingTasks = new Set(); // 正在处理的任务ID集合
        this.taskStatusCache = new Map(); // 任务状态缓存，减少数据库查询
        this.statusCacheTimeout = 5000; // 缓存5秒
    }

    /**
     * 获取任务状态（带缓存）
     * @param {number} taskId 任务ID
     * @returns {Promise<string|null>} 任务状态
     */
    async getTaskStatus(taskId) {
        const cacheKey = `task_${taskId}`;
        const cached = this.taskStatusCache.get(cacheKey);
        
        if (cached && Date.now() - cached.timestamp < this.statusCacheTimeout) {
            return cached.status;
        }

        try {
            const result = await database.get('SELECT status FROM async_tasks WHERE id = ?', [taskId]);
            const status = result?.status || null;
            
            // 更新缓存
            this.taskStatusCache.set(cacheKey, {
                status,
                timestamp: Date.now()
            });
            
            return status;
        } catch (error) {
            console.error('获取任务状态失败:', error);
            return null;
        }
    }

    /**
     * 清除任务状态缓存
     * @param {number} taskId 任务ID
     */
    clearTaskStatusCache(taskId) {
        this.taskStatusCache.delete(`task_${taskId}`);
    }

    /**
     * 更新任务状态（带缓存清理）
     * @param {number} taskId 任务ID
     * @param {string} status 新状态
     * @param {Object} additionalFields 额外字段
     * @param {Array} whereConditions 额外的WHERE条件，格式：['field = ?', value]
     */
    async updateTaskStatusSafe(taskId, status, additionalFields = {}, whereConditions = []) {
        try {
            // 验证字段名安全性（白名单）
            const allowedFields = [
                'started_at', 'completed_at', 'paused_at', 'error_message', 
                'retry_count', 'current_product_id', 'current_sequence', 
                'progress_percentage', 'success_count', 'failed_count', 'task_count'
            ];

            const updates = ['status = ?', 'updated_at = datetime(\'now\', \'localtime\')'];
            const params = [status];

            // 添加额外字段（安全处理）
            Object.entries(additionalFields).forEach(([key, value]) => {
                // 验证字段名
                if (!allowedFields.includes(key)) {
                    throw new Error(`不允许更新字段: ${key}`);
                }

                // 处理特殊值
                if (value === null) {
                    updates.push(`${key} = NULL`);
                } else if (value === 'NOW()') {
                    updates.push(`${key} = datetime('now', 'localtime')`);
                } else {
                    // 使用参数化查询
                    updates.push(`${key} = ?`);
                    params.push(value);
                }
            });

            // 构建WHERE子句
            let whereClause = 'WHERE id = ?';
            params.push(taskId);

            if (whereConditions.length > 0) {
                whereConditions.forEach(condition => {
                    if (Array.isArray(condition) && condition.length === 2) {
                        whereClause += ` AND ${condition[0]}`;
                        params.push(condition[1]);
                    } else if (typeof condition === 'string') {
                        // 对于不需要参数的条件，如 "status IN ('pending', 'running')"
                        whereClause += ` AND ${condition}`;
                    }
                });
            }

            await database.run(`
                UPDATE async_tasks 
                SET ${updates.join(', ')}
                ${whereClause}
            `, params);

            // 清除缓存
            this.clearTaskStatusCache(taskId);
        } catch (error) {
            console.error('更新任务状态失败:', error);
            throw error;
        }
    }

    /**
     * 创建新任务（使用事务确保原子性）
     * @param {Object} taskData 任务数据
     * @param {string} taskData.taskName 任务名称
     * @param {string} taskData.taskType 任务类型：hand_card, audio, live
     * @param {string} taskData.anchorName 主播名
     * @param {string} taskData.liveId 直播ID
     * @param {Array} taskData.productIds 产品ID列表
     * @param {Object} taskData.params 任务参数
     * @param {number} taskData.priority 优先级
     * @returns {Promise<number>} 返回任务ID
     */
    async createTask(taskData) {
        const {
            taskName,
            taskType,
            anchorName,
            liveId,
            productIds = [],
            params = {},
            priority = 5
        } = taskData;

        // 使用数据库事务确保原子性
        return await database.transaction(async () => {
            // 所有任务类型都使用相同的初始状态
            let initialStatus = 'pending';

            // 插入主任务
            const taskResult = await database.run(`
                INSERT INTO async_tasks (
                    task_name, task_type, anchor_name, live_id, task_params,
                    priority, task_count, status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'), datetime('now', 'localtime'))
            `, [
                taskName,
                taskType,
                anchorName,
                liveId,
                JSON.stringify(params),
                priority,
                productIds.length,
                initialStatus
            ]);

            const taskId = taskResult.meta.last_row_id;

            if (!taskId) {
                throw new Error('创建主任务失败，未获取到任务ID');
            }

            // 插入子任务详情
            if (productIds.length > 0) {
                // 一次性获取所有产品信息
                const placeholders = productIds.map(() => '?').join(',');
                const productsResult = await database.all(
                    `SELECT product_id, product_name, product_sequence
                     FROM live_products
                     WHERE live_id = ? AND product_id IN (${placeholders})
                     ORDER BY product_sequence DESC`,
                    [liveId, ...productIds]
                );

                const products = productsResult.results || [];
                
                if (products.length === 0) {
                    throw new Error('未找到任何匹配的产品信息');
                }

                // 批量插入子任务（在事务中）
                const insertPromises = products.map(product => 
                    database.run(`
                        INSERT INTO task_details (
                            task_id, product_id, product_name, sequence_number, status,
                            created_at
                        ) VALUES (?, ?, ?, ?, 'pending', datetime('now', 'localtime'))
                    `, [
                        taskId,
                        product.product_id,
                        product.product_name,
                        product.product_sequence
                    ])
                );

                await Promise.all(insertPromises);
            }

            console.log(`✅ 创建任务成功: ${taskName} (ID: ${taskId})`);
            
            // 清除相关任务的状态缓存
            this.clearTaskStatusCache(taskId);
            
            // 尝试启动任务处理（在事务完成后）
            setImmediate(() => this.processNextTask());
            
            return taskId;
        });
    }

    /**
     * 获取任务列表
     * @param {Object} filters 过滤条件
     * @returns {Promise<Array>} 任务列表
     */
    async getTaskList(filters = {}) {
        let sql = `
            SELECT
                id, task_name, task_type, anchor_name, live_id,
                task_count, success_count, failed_count,
                current_product_id, current_sequence, progress_percentage,
                status, error_message, retry_count, max_retries,
                current_event, created_at, started_at, completed_at, updated_at
            FROM async_tasks
        `;
        
        const conditions = [];
        const params = [];

        if (filters.status) {
            if (Array.isArray(filters.status)) {
                // 多个状态值，使用OR条件
                const statusPlaceholders = filters.status.map(() => '?').join(',');
                conditions.push(`status IN (${statusPlaceholders})`);
                params.push(...filters.status);
            } else {
                // 单个状态值
                conditions.push('status = ?');
                params.push(filters.status);
            }
        }
        
        if (filters.taskType) {
            if (Array.isArray(filters.taskType)) {
                // 多个任务类型，使用OR条件
                const typePlaceholders = filters.taskType.map(() => '?').join(',');
                conditions.push(`task_type IN (${typePlaceholders})`);
                params.push(...filters.taskType);
            } else {
                // 单个任务类型
                conditions.push('task_type = ?');
                params.push(filters.taskType);
            }
        }
        
        if (filters.anchorName) {
            conditions.push('anchor_name = ?');
            params.push(filters.anchorName);
        }

        if (conditions.length > 0) {
            sql += ' WHERE ' + conditions.join(' AND ');
        }

        sql += ` ORDER BY 
            created_at DESC,
            CASE status 
                WHEN 'running' THEN 1
                WHEN 'pending' THEN 2
                WHEN 'paused' THEN 3
                WHEN 'failed' THEN 4
                WHEN 'completed' THEN 5
                WHEN 'cancelled' THEN 6
                ELSE 7
            END`;

        const result = await database.all(sql, params);
        return result.results || [];
    }

    /**
     * 获取任务详情
     * @param {number} taskId 任务ID
     * @returns {Promise<Object>} 任务详情
     */
    async getTaskDetails(taskId) {
        // 获取主任务信息
        const taskResult = await database.get(
            'SELECT * FROM async_tasks WHERE id = ?',
            [taskId]
        );

        if (!taskResult) {
            throw new Error('任务不存在');
        }

        // 获取子任务详情（倒序显示，从大序号开始）
        const detailsResult = await database.all(
            'SELECT * FROM task_details WHERE task_id = ? ORDER BY sequence_number DESC',
            [taskId]
        );

        return {
            task: taskResult,
            details: detailsResult.results || []
        };
    }



    /**
     * 处理下一个待执行任务
     */
    async processNextTask() {
        // 获取待执行任务列表，按优先级和创建时间排序
        const pendingTasks = await database.all(`
            SELECT * FROM async_tasks
            WHERE status = 'pending'
            ORDER BY priority DESC, created_at ASC
        `);

        const tasks = pendingTasks.results || [];

        if (tasks.length === 0) {
            return; // 没有待执行任务
        }

        for (const task of tasks) {
            // 检查任务是否已经在处理中
            if (this.processingTasks.has(task.id)) {
                continue;
            }

            // 所有任务类型都可以并发执行
            console.log(`🚀 启动任务执行: ${task.task_name} (ID: ${task.id}, 类型: ${task.task_type})`);
            this.executeTaskAsync(task);
        }
    }

    /**
     * 清理任务资源
     * @param {Object} task 任务对象
     * @param {string} reason 清理原因
     */
    async cleanupTaskResources(task, reason = 'normal') {
        try {
            console.log(`🧹 开始清理任务资源: ${task.task_name} (ID: ${task.id}), 原因: ${reason}`);
            
            // 清理状态缓存
            this.clearTaskStatusCache(task.id);
            
            // 清理当前产品信息字段（暂停时保留，其他情况清除）
            // if (reason !== 'paused') {
            //     await database.run(`
            //         UPDATE async_tasks 
            //         SET current_product_id = NULL, current_sequence = NULL, updated_at = datetime('now', 'localtime')
            //         WHERE id = ?
            //     `, [task.id]);
            //     console.log(`🧹 已清除任务 ${task.id} 的当前产品信息字段`);
            // } else {
            //     console.log(`⏸️ 任务暂停，保留当前产品信息字段以便恢复时显示`);
            // }
            
            // 如果是音频任务，清理GPU资源
            if (task.task_type === 'audio') {
                await this.cleanupAudioTaskResources(task, reason);
            }
            
            // 如果是手卡任务，清理相关资源
            if (task.task_type === 'hand_card') {
                await this.cleanupHandCardTaskResources(task);
            }
            
            console.log(`✅ 任务资源清理完成: ${task.task_name} (ID: ${task.id})`);
        } catch (error) {
            console.error(`❌ 清理任务资源失败: ${task.task_name} (ID: ${task.id})`, error);
        }
    }

    /**
     * 清理音频任务资源
     * @param {Object} task 任务对象
     * @param {string} reason 清理原因
     */
    async cleanupAudioTaskResources(task, reason = 'completed') {
        try {
            const taskParams = JSON.parse(task.task_params || '{}');
            const { autoDestroy, instanceId, publicId, autoCreateGpu } = taskParams;

            // 只有在任务完成或取消时才销毁GPU实例，暂停时保留实例
            if (reason !== 'paused') {
                await handleGpuInstanceDestroy(taskParams, reason, 5000); // 5秒后异步销毁
            } else {
                const displayId = publicId || instanceId;
                console.log(`⏸️ 任务暂停，保留GPU实例: ${displayId}`);
            }
        } catch (error) {
            console.warn('清理音频任务资源时出错:', error);
        }
    }

    /**
     * 清理手卡任务资源
     * @param {Object} task 任务对象
     */
    async cleanupHandCardTaskResources(task) {
        try {
            // 清理可能的临时文件或资源
            console.log(`🧹 清理手卡任务资源: ${task.task_name}`);
            // 暂时没有特殊资源需要清理
        } catch (error) {
            console.warn('清理手卡任务资源时出错:', error);
        }
    }

    /**
     * 异步执行任务（不阻塞其他任务）- 增强版本
     */
    async executeTaskAsync(task) {
        // 将任务添加到处理中集合
        this.processingTasks.add(task.id);
        let taskCompleted = false;

        try {
            // 设置任务超时保护（可选）
            const taskTimeout = this.getTaskTimeout(task.task_type);
            let timeoutId = null;
            
            if (taskTimeout > 0) {
                timeoutId = setTimeout(() => {
                    if (!taskCompleted) {
                        console.warn(`⏰ 任务执行超时: ${task.task_name} (ID: ${task.id}), 超时时间: ${taskTimeout}ms`);
                        // 这里可以选择取消任务或记录超时信息
                    }
                }, taskTimeout);
            }

            await this.executeTask(task);
            taskCompleted = true;
            
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
            
        } catch (error) {
            // 检查任务当前状态，如果是暂停或取消，不标记为失败
            const currentStatus = await this.getTaskStatus(task.id);
            if (currentStatus === 'paused') {
                console.log(`⏸️ 任务已暂停，不标记为失败: ${task.task_name} (ID: ${task.id})`);
                // 不设置taskCompleted，让finally块正确处理
            } else if (currentStatus === 'cancelled') {
                console.log(`🛑 任务已取消，不标记为失败: ${task.task_name} (ID: ${task.id})`);
                // 不设置taskCompleted，让finally块正确处理
            } else {
                taskCompleted = true;
                console.error('任务执行失败:', error);

                // 增强的错误处理
                const errorInfo = this.analyzeTaskError(error, task);
                await this.markTaskFailed(task.id, errorInfo.message, errorInfo.shouldRetry);
            }

        } finally {
            // 从处理中集合移除任务
            this.processingTasks.delete(task.id);

            // 检查任务最终状态，决定清理策略
            const finalStatus = await this.getTaskStatus(task.id);
            let cleanupReason = 'failed';

            // 优先根据任务的最终状态决定清理策略，而不是依赖taskCompleted标志
            if (finalStatus === 'paused') {
                cleanupReason = 'paused';
            } else if (finalStatus === 'cancelled') {
                cleanupReason = 'cancelled';
            } else if (finalStatus === 'completed' || taskCompleted) {
                cleanupReason = 'completed';
            }

            // 清理任务资源（暂停时不清理GPU实例）
            await this.cleanupTaskResources(task, cleanupReason);

            // 继续处理下一个任务（延迟执行以避免过于频繁）
            setTimeout(() => this.processNextTask(), 1000);
        }
    }

    /**
     * 获取任务超时时间
     * @param {string} taskType 任务类型
     * @returns {number} 超时时间（毫秒），0表示不设置超时
     */
    getTaskTimeout(taskType) {
        const timeouts = {
            'hand_card': 30 * 60 * 1000, // 30分钟
            'audio': 60 * 60 * 1000,     // 60分钟
            'live': 0                    // 不设置超时
        };
        return timeouts[taskType] || 0;
    }

    /**
     * 分析任务错误
     * @param {Error} error 错误对象
     * @param {Object} task 任务对象
     * @returns {Object} 错误分析结果
     */
    analyzeTaskError(error, task) {
        let shouldRetry = false;
        let message = error.message || '未知错误';

        // 检查是否是可重试的错误
        const retryableErrors = [
            'network',
            'timeout',
            'connection',
            'temporary'
        ];

        const isRetryable = retryableErrors.some(keyword => 
            message.toLowerCase().includes(keyword)
        );

        // 对于手卡任务的特殊处理
        if (task.task_type === 'hand_card' && error.isTokenExpired) {
            message = '令牌过期，任务停止执行';
            shouldRetry = false;
        } else if (isRetryable && task.retry_count < task.max_retries) {
            shouldRetry = true;
        }

        return { message, shouldRetry };
    }

    /**
     * 执行具体任务
     * @param {Object} task 任务对象
     */
    async executeTask(task) {
        console.log(`🚀 开始执行任务: ${task.task_name} (ID: ${task.id})`);

        // 更新任务状态为执行中
        await this.updateTaskStatusSafe(task.id, 'running', {
            'started_at': 'NOW()'  // 使用简化的NOW()标记
        });

        const taskParams = JSON.parse(task.task_params || '{}');

        try {
            switch (task.task_type) {
                case 'hand_card':
                    await this.executeHandCardTask(task, taskParams);
                    break;
                case 'audio':
                    await this.executeAudioTask(task, taskParams);
                    break;
                case 'live':
                    await this.executeLiveTask(task, taskParams);
                    break;
                default:
                    throw new Error(`未知任务类型: ${task.task_type}`);
            }

            // 检查任务是否在执行过程中被取消或暂停
            const currentStatus = await this.getTaskStatus(task.id);
            if (currentStatus === 'cancelled') {
                console.log(`⚠️ 任务在执行过程中被取消: ${task.task_name} (ID: ${task.id})`);
                return;
            }
            if (currentStatus === 'paused') {
                console.log(`⏸️ 任务在执行过程中被暂停: ${task.task_name} (ID: ${task.id})`);
                return;
            }

            // 标记任务完成
            await this.markTaskCompleted(task.id);
            console.log(`✅ 任务执行完成: ${task.task_name} (ID: ${task.id})`);
        } catch (error) {
            // 检查是否是取消操作导致的异常
            const currentStatus = await this.getTaskStatus(task.id);
            if (currentStatus === 'cancelled') {
                console.log(`⚠️ 任务被取消: ${task.task_name} (ID: ${task.id})`);
                return;
            }
            throw error;
        }
    }

    /**
     * 执行手卡提取任务 - 线程池并发版本
     */
    async executeHandCardTask(task, params) {
        // 获取主播cookie
        const anchor = await database.get(
            'SELECT anchor_cookie FROM anchors WHERE anchor_name = ?',
            [task.anchor_name]
        );

        if (!anchor?.anchor_cookie) {
            throw new Error('未找到主播cookie');
        }

        // 从主播cookie中提取h5Token
        let h5Token = '';
        try {
            const cookies = anchor.anchor_cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === '_m_h5_tk' && value) {
                    h5Token = value.split('_')[0];
                    break;
                }
            }
        } catch (e) {
            console.error('解析cookie失败:', e);
        }

        if (!h5Token) {
            throw new Error('主播cookie无效，缺少h5_tk');
        }

        // 获取待处理的产品列表
        const detailsResult = await database.all(
            'SELECT * FROM task_details WHERE task_id = ? AND status = ? ORDER BY sequence_number',
            [task.id, 'pending']
        );

        const details = detailsResult.results || [];

        // 获取任务总数（用于正确计算进度）
        const totalTasksResult = await database.get(
            'SELECT COUNT(*) as total FROM task_details WHERE task_id = ?',
            [task.id]
        );
        const totalTasks = totalTasksResult.total || 0;

        if (details.length === 0) {
            console.log(`📋 任务 ${task.task_name} 没有待处理的子任务`);
            return;
        }

        // 使用线程池并发处理
        await this.processHandCardWithThreadPool(task, details, h5Token, anchor.anchor_cookie, params);
    }

    /**
     * 线程池并发处理手卡提取
     */
    async processHandCardWithThreadPool(task, details, h5Token, cookie, params) {
        const POOL_SIZE = 10;           // 线程池大小
        const BATCH_SIZE = 5;          // 每个线程批量处理数量
        const MAX_RETRY = 3;           // 最大重试次数

        let success = 0;
        let failed = 0;
        const total = details.length;
        let isTokenExpired = false;

        // 任务队列管理
        const taskQueue = [...details];
        const retryQueue = [];
        const retryCount = new Map();

        // 线程池状态
        let activeThreads = 0;
        let isCompleted = false;

        const startTime = Date.now();

        const extractor = new LiveCardExtractor();

        // Cookie更新状态管理（避免并发更新）
        let cookieUpdateInProgress = false;
        let cookieUpdatePromise = null;
        let currentCookie = cookie;
        let currentH5Token = h5Token;

        // 检查任务是否被取消或暂停
        const checkTaskCancelled = async () => {
            try {
                const currentStatus = await this.getTaskStatus(task.id);
                return currentStatus === 'cancelled';
            } catch (error) {
                console.error('检查任务状态失败:', error);
                return false;
            }
        };

        const checkTaskPaused = async () => {
            try {
                const currentStatus = await this.getTaskStatus(task.id);
                return currentStatus === 'paused';
            } catch (error) {
                console.error('检查任务状态失败:', error);
                return false;
            }
        };

        // 统一的Cookie更新机制（避免并发更新）
        const updateCookieIfNeeded = async () => {
            if (cookieUpdateInProgress) {
                // 如果已经有更新在进行，等待完成
                return await cookieUpdatePromise;
            }

            cookieUpdateInProgress = true;
            cookieUpdatePromise = (async () => {
                try {
                    console.log(`🔄 开始更新主播 ${task.anchor_name} 的Cookie...`);
                    const cookieUpdateResult = await this.updateAnchorCookieForTask(task.anchor_name);

                    if (cookieUpdateResult.success) {
                        // 获取更新后的cookie和h5Token
                        const updatedAnchor = await database.get(
                            'SELECT anchor_cookie FROM anchors WHERE anchor_name = ?',
                            [task.anchor_name]
                        );

                        if (updatedAnchor?.anchor_cookie) {
                            // 提取新的h5Token
                            const { extractH5Token } = await import('./utils/cookie-utils.js');
                            const newH5Token = extractH5Token(updatedAnchor.anchor_cookie);

                            if (newH5Token) {
                                currentCookie = updatedAnchor.anchor_cookie;
                                currentH5Token = newH5Token;
                                console.log(`✅ Cookie更新成功，新h5Token: ${newH5Token.substring(0, 10)}...`);
                                return { success: true, cookie: currentCookie, h5Token: currentH5Token };
                            }
                        }
                    }

                    console.log(`❌ Cookie更新失败: ${cookieUpdateResult.error}`);
                    return { success: false, error: cookieUpdateResult.error };
                } catch (error) {
                    console.log(`❌ Cookie更新异常: ${error.message}`);
                    return { success: false, error: error.message };
                } finally {
                    cookieUpdateInProgress = false;
                    cookieUpdatePromise = null;
                }
            })();

            return await cookieUpdatePromise;
        };

        // 处理单个产品的手卡提取（复用逻辑）
        const extractSingleCard = async (detail, useCurrentTokens = true) => {
            const tokenToUse = useCurrentTokens ? currentH5Token : h5Token;
            const cookieToUse = useCurrentTokens ? currentCookie : cookie;

            return await extractor.extractCard(
                task.live_id,
                detail.product_id,
                tokenToUse,
                cookieToUse,
                task.anchor_name,
                params
            );
        };

        return new Promise((resolve, reject) => {
            // 获取下一批任务
            const getNextBatch = () => {
                const batch = [];

                // 优先处理重试队列
                while (retryQueue.length > 0 && batch.length < BATCH_SIZE) {
                    batch.push(retryQueue.shift());
                }

                // 补充新任务
                while (taskQueue.length > 0 && batch.length < BATCH_SIZE) {
                    batch.push(taskQueue.shift());
                }

                return batch;
            };

            // 智能重试延迟（指数退避）
            const getRetryDelay = (retryCount) => {
                return Math.min(1000 * Math.pow(2, retryCount), 5000);
            };

            // 批量处理手卡
            const processBatch = async (batchDetails) => {
                const results = [];
                
                for (const detail of batchDetails) {
                    // 在处理每个产品之前检查任务是否被取消或暂停
                    if (await checkTaskCancelled()) {
                        console.log(`🛑 产品 ${detail.product_id} 处理前检测到任务被取消`);
                        // 将剩余产品标记为取消
                        results.push({
                            detail,
                            success: false,
                            data: null,
                            error: '任务已被取消',
                            isCancelled: true
                        });
                        continue;
                    }

                    if (await checkTaskPaused()) {
                        console.log(`⏸️ 产品 ${detail.product_id} 处理前检测到任务被暂停`);
                        // 将剩余产品标记为暂停
                        results.push({
                            detail,
                            success: false,
                            data: null,
                            error: '任务已被暂停',
                            isPaused: true
                        });
                        continue;
                    }

                    try {
                        // 更新当前执行状态
                        await this.updateTaskProgress(task.id, detail.product_id, detail.sequence_number);
                        
                        // 更新子任务状态为运行中
                        await database.run(
                            'UPDATE task_details SET status = ?, started_at = datetime(\'now\', \'localtime\') WHERE id = ?',
                            ['running', detail.id]
                        );

                        console.log(`🎯 开始提取产品 ${detail.product_id} 的手卡...`);

                        // 执行手卡提取（使用统一方法）
                        const card = await extractSingleCard(detail, true);

                        if (card && card.script) {
                            let logMessage = `✅ 产品 ${detail.product_id} 手卡提取成功，内容长度: ${card.script.length}`;

                            // 如果进行了违禁词过滤，添加过滤信息到日志
                            if (card.isFiltered !== undefined) {
                                if (card.isFiltered && card.filteredWords) {
                                    logMessage += `，已过滤违禁词: [${card.filteredWords.join(', ')}]`;
                                } else if (card.isFiltered === false) {
                                    logMessage += `，未发现违禁词`;
                                }

                                if (card.filterError) {
                                    logMessage += `，过滤出错: ${card.filterError}`;
                                }
                            }

                            console.log(logMessage);

                            results.push({
                                detail,
                                success: true,
                                data: card,
                                error: null
                            });
                        } else {
                            const errorMsg = card ? '手卡内容为空' : '未获取到手卡数据';
                            results.push({
                                detail,
                                success: false,
                                data: null,
                                error: errorMsg
                            });
                        }

                    } catch (error) {
                        console.error(`❌ 产品 ${detail.product_id} 手卡提取失败:`, error);
                        
                        results.push({
                            detail,
                            success: false,
                            data: null,
                            error: error.message,
                            isTokenExpired: error.isTokenExpired
                        });
                    }

                }
                
                return results;
            };

            // 处理批次结果
            const handleBatchResults = async (batchResults) => {
                for (const result of batchResults) {
                    const { detail, success: isSuccess, data, error, isTokenExpired: tokenExpired, isCancelled, isPaused } = result;
                    
                    // 检查是否因为取消而停止处理
                    if (isCancelled) {
                        console.log(`🛑 产品 ${detail.product_id} 因任务取消而停止处理`);

                        // 更新当前子任务为取消
                        await database.run(`
                            UPDATE task_details
                            SET status = 'cancelled', error_message = ?, completed_at = datetime('now', 'localtime')
                            WHERE id = ?
                        `, [error, detail.id]);

                        failed++;
                        isCompleted = true;
                        break;
                    }

                    // 检查是否因为暂停而停止处理
                    if (isPaused) {
                        console.log(`⏸️ 产品 ${detail.product_id} 因任务暂停而停止处理`);

                        // 保持当前子任务为pending状态，以便恢复时继续处理
                        isCompleted = true;
                        break;
                    }
                    
                    // 检查令牌过期错误
                    if (tokenExpired) {
                        console.log(`🛑 检测到令牌过期错误，尝试更新cookie并重新提取`);

                        try {
                            // 统一更新cookie（避免并发更新）
                            const updateResult = await updateCookieIfNeeded();

                            if (updateResult.success) {
                                console.log(`✅ Cookie更新成功，重新提取产品 ${detail.product_id}`);

                                // 使用更新后的token重新提取
                                const retryResult = await extractSingleCard(detail, true);

                                if (retryResult && retryResult.script) {
                                    console.log(`✅ 产品 ${detail.product_id} 重新提取成功`);

                                    // 保存手卡内容到产品表
                                    await database.run(
                                        'UPDATE live_products SET script_content = ?, updated_at = datetime(\'now\', \'localtime\') WHERE live_id = ? AND product_id = ?',
                                        [retryResult.script, task.live_id, detail.product_id]
                                    );

                                    // 更新子任务为成功
                                    await database.run(`
                                        UPDATE task_details
                                        SET status = 'completed', result_data = ?, completed_at = datetime('now', 'localtime')
                                        WHERE id = ?
                                    `, [JSON.stringify(retryResult), detail.id]);

                                    success++;
                                    console.log(`🔄 Cookie已更新，继续处理后续任务`);
                                    continue; // 继续处理下一个任务
                                } else {
                                    console.log(`❌ 产品 ${detail.product_id} 重新提取仍然失败`);
                                }
                            } else {
                                console.log(`❌ Cookie更新失败: ${updateResult.error}`);
                            }
                        } catch (updateError) {
                            console.log(`❌ Cookie更新异常: ${updateError.message}`);
                        }

                        // 如果cookie更新失败或重新提取失败，则按原逻辑处理
                        console.log(`🛑 Cookie更新或重新提取失败，停止整个任务执行`);
                        isTokenExpired = true;
                        isCompleted = true;

                        // 更新当前子任务为失败
                        await database.run(`
                            UPDATE task_details
                            SET status = 'failed', error_message = ?, completed_at = datetime('now', 'localtime')
                            WHERE id = ?
                        `, [error, detail.id]);

                        // 将所有剩余的待处理子任务标记为取消
                        await database.run(`
                            UPDATE task_details
                            SET status = 'cancelled', error_message = '因令牌过期而取消', completed_at = datetime('now', 'localtime')
                            WHERE task_id = ? AND status = 'pending'
                        `, [task.id]);

                        failed++;
                        break;
                    }
                    
                    if (isSuccess) {
                        // 保存手卡内容到产品表
                        await database.run(
                            'UPDATE live_products SET script_content = ?, updated_at = datetime(\'now\', \'localtime\') WHERE live_id = ? AND product_id = ?',
                            [data.script, task.live_id, detail.product_id]
                        );

                        // 更新子任务为成功
                        await database.run(`
                            UPDATE task_details
                            SET status = 'completed', result_data = ?, completed_at = datetime('now', 'localtime')
                            WHERE id = ?
                        `, [JSON.stringify(data), detail.id]);

                        success++;
                    } else {
                        const currentRetry = retryCount.get(detail.id) || 0;
                        
                        if (currentRetry < MAX_RETRY) {
                            // 加入重试队列
                            retryCount.set(detail.id, currentRetry + 1);
                            const delay = getRetryDelay(currentRetry);
                            
                            console.log(`🔄 产品 ${detail.product_id} 将在${delay}ms后重试 (第${currentRetry + 1}次)`);
                            
                            setTimeout(() => {
                                retryQueue.push(detail);
                            }, delay);
                        } else {
                            // 超过重试次数，标记为失败
                            await database.run(`
                                UPDATE task_details
                                SET status = 'failed', error_message = ?, completed_at = datetime('now', 'localtime')
                                WHERE id = ?
                            `, [error, detail.id]);
                            
                            failed++;
                            console.log(`❌ 产品 ${detail.product_id} 最终失败: ${error}`);
                        }
                    }
                }

                // 更新主任务统计
                await database.run(
                    'UPDATE async_tasks SET success_count = ?, failed_count = ?, updated_at = datetime(\'now\', \'localtime\') WHERE id = ?',
                    [success, failed, task.id]
                );

                // 更新进度百分比
                await this.updateProgressPercentage(task.id);
            };

            // 工作线程
            const workerThread = async (threadId) => {
                
                try {
                    while (!isCompleted && !isTokenExpired) {
                        // 定期检查任务是否被取消
                        if (await checkTaskCancelled()) {
                            console.log(`🛑 线程${threadId} 检测到任务被取消，停止执行`);
                            isCompleted = true;
                            break;
                        }

                        const batch = getNextBatch();
                        
                        if (batch.length === 0) {
                            // 检查是否真的完成了
                            if (success + failed >= total) {
                                break;
                            }
                            // 等待重试任务
                            await new Promise(resolve => setTimeout(resolve, 300));
                            continue;
                        }

                        const batchResults = await processBatch(batch);
                        await handleBatchResults(batchResults);
                        
                        // 检查是否完成或遇到令牌过期
                        if (success + failed >= total || isTokenExpired) {
                            isCompleted = true;
                            break;
                        }
                    }
                } catch (error) {
                    console.error(`❌ 线程${threadId} 异常:`, error);
                } finally {
                    activeThreads--;
                    
                    // 最后一个线程完成时，处理结果
                    if (activeThreads === 0) {
                        const endTime = Date.now();
                        const duration = ((endTime - startTime) / 1000).toFixed(2);
                        const successRate = total > 0 ? ((success / total) * 100).toFixed(1) : '0';
                        const avgSpeed = total > 0 ? (total / (endTime - startTime) * 1000).toFixed(1) : '0';
                        
                        // 检查是否因为取消而结束
                        if (await checkTaskCancelled()) {
                            reject(new Error(`任务已被取消。已处理${success + failed}/${total}个产品`));
                        } else if (isTokenExpired) {
                            reject(new Error(`令牌过期，任务中断。已处理${success + failed}/${total}个产品`));
                        } else {
                            resolve();
                        }
                    }
                }
            };

            // 启动线程池
            activeThreads = POOL_SIZE;
            for (let i = 1; i <= POOL_SIZE; i++) {
                setTimeout(() => workerThread(i), i * 100); // 错开启动时间
            }
        });
    }

    /**
     * 执行音频生成任务
     */
    async executeAudioTask(task, params) {
        console.log(`🎵 开始执行音频生成任务: ${task.task_name}`);

        // 获取任务参数
        const {
            speed = 1.2,
            randomSpeed = false,
            autoDestroy = false,
            concurrency = 20,
            autoCreateGpu = false,
            instanceId,
            publicId,
            anchorName
        } = params;

        let finalInstanceId = instanceId;
        let finalPublicId = publicId;

        // 如果启用自动创建GPU
        if (autoCreateGpu) {
            console.log('🚀 开始自动创建GPU实例...');
            await this.updateTaskCurrentEvent(task.id, '创建GPU中...');

            const gpuInstance = await createGpuInstance(anchorName);
            finalInstanceId = gpuInstance.instanceId;
            finalPublicId = gpuInstance.publicId;

            // 更新任务参数中的实例信息
            const updatedParams = { ...params, instanceId: finalInstanceId, publicId: finalPublicId };
            await database.run(
                'UPDATE async_tasks SET task_params = ? WHERE id = ?',
                [JSON.stringify(updatedParams), task.id]
            );
        }

        if (!finalInstanceId || !finalPublicId || !anchorName) {
            throw new Error('缺少必要的GPU实例参数');
        }

        // 获取GPU账户信息
        const gpuAccount = await getAvailableGpuAccount();
        if (!gpuAccount) {
            throw new Error('无法获取可用的GPU账户');
        }

        // 获取待处理的子任务列表（倒序处理，从大序号开始）
        const detailsResult = await database.all(
            'SELECT * FROM task_details WHERE task_id = ? AND status = ? ORDER BY sequence_number DESC',
            [task.id, 'pending']
        );
        const details = detailsResult.results || [];

        // 获取任务总数（用于正确计算进度）
        const totalTasksResult = await database.get(
            'SELECT COUNT(*) as total FROM task_details WHERE task_id = ?',
            [task.id]
        );
        const totalTasks = totalTasksResult.total || 0;

        if (details.length === 0) {
            console.log('没有待处理的音频生成子任务');
            return;
        }

        console.log(`📋 找到 ${details.length} 个待处理的音频生成任务，使用 ${concurrency} 个并发线程`);

        // 如果是自动创建的GPU，需要等待容器就绪
        if (autoCreateGpu) {
            console.log('⏳ 等待GPU容器就绪...');
            await this.updateTaskCurrentEvent(task.id, '容器状态检查中...');
            await this.waitForContainerReady(finalInstanceId);
        }

        // 更新当前事件为音频生成
        await this.updateTaskCurrentEvent(task.id, '音频生成中...');

        // 使用线程池并发处理音频生成，传递更新后的参数
        const updatedParams = { ...params, instanceId: finalInstanceId, publicId: finalPublicId };
        await this.processAudioWithThreadPool(task, details, gpuAccount, updatedParams);

        // 检查任务当前状态，如果被暂停或取消则不执行完成逻辑
        const currentStatus = await this.getTaskStatus(task.id);
        if (currentStatus === 'paused') {
            console.log(`⏸️ 任务已暂停，跳过完成处理逻辑: ${task.task_name} (ID: ${task.id})`);
            return;
        }
        if (currentStatus === 'cancelled') {
            console.log(`🛑 任务已取消，跳过完成处理逻辑: ${task.task_name} (ID: ${task.id})`);
            return;
        }

        // 在所有子任务处理完成后，更新主任务的最终进度
        const allTasksResult = await database.all(
            'SELECT status FROM task_details WHERE task_id = ?',
            [task.id]
        );
        const allTasks = allTasksResult.results || [];
        const totalSuccessCount = allTasks.filter(t => t.status === 'completed').length;
        const totalFailedCount = allTasks.filter(t => t.status === 'failed').length;
        const totalCompletedCount = totalSuccessCount + totalFailedCount;
        const progressPercentage = totalTasks > 0 ? Math.round((totalCompletedCount / totalTasks) * 100) : 0;

        await database.run(`
            UPDATE async_tasks
            SET success_count = ?, failed_count = ?, progress_percentage = ?, updated_at = datetime('now', 'localtime')
            WHERE id = ?
        `, [totalSuccessCount, totalFailedCount, progressPercentage, task.id]);



        // 任务完成后处理
        console.log(`🎵 音频生成任务执行完成: 成功 ${totalSuccessCount}, 失败 ${totalFailedCount}`);

        // 生成产品ID文件
        await this.generateProductIdFile(task, anchorName);
    }

    /**
     * 使用线程池并发处理音频生成任务
     */
    async processAudioWithThreadPool(task, details, gpuAccount, params) {
        const { speed, randomSpeed, concurrency, instanceId, publicId, anchorName } = params;
        const POOL_SIZE = Math.min(concurrency, details.length);

        console.log(`🚀 启动音频生成线程池，并发数: ${POOL_SIZE}，总任务数: ${details.length}`);

        // 使用队列管理任务分配，避免竞态条件
        const taskQueue = [...details];
        const results = {
            success: 0,
            failed: 0,
            isTokenExpired: false,
            isCancelled: false,
            isPaused: false
        };

        const startTime = Date.now();
        let lastProgressTime = 0;
        const PROGRESS_INTERVAL = 2000; // 2秒更新一次进度

        // 检查任务状态
        const checkTaskStatus = async () => {
            const currentStatus = await this.getTaskStatus(task.id);
            return {
                isCancelled: currentStatus === 'cancelled',
                isPaused: currentStatus === 'paused'
            };
        };

        // 处理单个音频任务
        const processAudioTask = async (detail, threadId) => {
            try {
                const audioParams = JSON.parse(detail.result_data || '{}');
                const { text, filename, productId, sequence } = audioParams;

                if (!text || !filename || !productId) {
                    throw new Error('音频生成参数不完整');
                }

                console.log(`🎤 线程${threadId} 处理产品 ${productId} (序号: ${sequence})`);

                // 更新当前执行状态到主任务
                await this.updateTaskProgress(task.id, productId, sequence);

                // 更新子任务状态
                await database.run(`
                    UPDATE task_details
                    SET status = 'in_progress', started_at = datetime('now', 'localtime')
                    WHERE id = ?
                `, [detail.id]);

                // 确定语速
                let actualSpeed = speed;
                if (randomSpeed) {
                    const randomSpeeds = [1.0, 1.1, 1.2];
                    actualSpeed = randomSpeeds[Math.floor(Math.random() * randomSpeeds.length)];
                }

                // 执行音频生成
                const result = await this.executeGpuAudioGeneration({
                    text,
                    speaker: `${anchorName}.pt`,
                    speed: actualSpeed,
                    filename,
                    publicId,
                    instanceId,
                    anchorName,
                    gpuToken: gpuAccount.token
                });

                if (result.success) {
                    results.success++;
                    console.log(`✅ 线程${threadId} 产品 ${productId} 成功`);

                    // 更新子任务状态
                    await database.run(`
                        UPDATE task_details
                        SET status = 'completed', completed_at = datetime('now', 'localtime'),
                            result_data = ?
                        WHERE id = ?
                    `, [JSON.stringify(result), detail.id]);

                    // 更新产品状态
                    await database.run(`
                        UPDATE live_products
                        SET audio_extraction_status = '已生成', updated_at = datetime('now', 'localtime')
                        WHERE product_id = ?
                    `, [productId]);
                } else {
                    throw new Error(result.error || '音频生成失败');
                }

            } catch (error) {
                results.failed++;
                console.error(`❌ 线程${threadId} 产品 ${detail.product_id} 失败: ${error.message}`);

                // 检查令牌过期（多种错误类型）
                if (error.isTokenExpired ||
                    (error.message && (
                        error.message.includes('令牌过期') ||
                        error.message.includes('FAIL_SYS_TOKEN') ||
                        error.message.includes('FAIL_SYS_SESSION')
                    ))) {
                    results.isTokenExpired = true;
                }

                // 更新子任务状态
                await database.run(`
                    UPDATE task_details
                    SET status = 'failed', error_message = ?, completed_at = datetime('now', 'localtime')
                    WHERE id = ?
                `, [error.message, detail.id]);

                // 更新产品状态
                if (detail.product_id) {
                    await database.run(`
                        UPDATE live_products
                        SET audio_extraction_status = '生成失败', updated_at = datetime('now', 'localtime')
                        WHERE product_id = ?
                    `, [detail.product_id]);
                }
            }
        };

        // 工作线程函数
        const workerThread = async (threadId) => {
            console.log(`🧵 线程${threadId} 启动`);

            while (taskQueue.length > 0 && !results.isTokenExpired && !results.isCancelled && !results.isPaused) {
                // 检查任务状态
                const statusCheck = await checkTaskStatus();
                if (statusCheck.isCancelled) {
                    results.isCancelled = true;
                    console.log(`🛑 线程${threadId} 检测到任务被取消`);
                    break;
                }
                if (statusCheck.isPaused) {
                    results.isPaused = true;
                    console.log(`⏸️ 线程${threadId} 检测到任务被暂停`);
                    break;
                }

                // 从队列中取任务（线程安全）
                const detail = taskQueue.shift();
                if (!detail) break;

                await processAudioTask(detail, threadId);

                // 定期更新进度和显示状态
                const now = Date.now();
                if (now - lastProgressTime > PROGRESS_INTERVAL) {
                    lastProgressTime = now;
                    await this.updateProgressPercentage(task.id);

                    const processed = results.success + results.failed;
                    const progressPercent = ((processed / details.length) * 100).toFixed(1);
                    console.log(`📊 总进度: ${processed}/${details.length} (${progressPercent}%) - 成功: ${results.success}, 失败: ${results.failed}`);
                }
            }

            console.log(`🧵 线程${threadId} 结束`);
        };

        // 启动所有工作线程
        const threadPromises = [];
        for (let i = 1; i <= POOL_SIZE; i++) {
            const promise = new Promise(resolve => {
                setTimeout(async () => {
                    await workerThread(i);
                    resolve();
                }, i * 50); // 错开启动时间
            });
            threadPromises.push(promise);
        }

        // 等待所有线程完成
        await Promise.all(threadPromises);

        // 最终进度更新
        await this.updateProgressPercentage(task.id);

        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);
        const successRate = details.length > 0 ? ((results.success / details.length) * 100).toFixed(1) : '0';

        console.log(`🎵 音频生成完成: 成功 ${results.success}, 失败 ${results.failed}, 耗时 ${duration}秒, 成功率 ${successRate}%`);

        // 检查结果状态
        if (results.isCancelled) {
            throw new Error(`任务已被取消。已处理${results.success + results.failed}/${details.length}个产品`);
        }
        if (results.isPaused) {
            // 暂停不抛出错误，正常结束，任务状态已经是paused
            console.log(`⏸️ 任务已暂停，已处理${results.success + results.failed}/${details.length}个产品`);
            return; // 正常返回，不抛出错误
        }
        if (results.isTokenExpired) {
            throw new Error(`令牌过期，任务中断。已处理${results.success + results.failed}/${details.length}个产品`);
        }
    }

    /**
     * 执行GPU音频生成
     */
    async executeGpuAudioGeneration(params) {
        const { text, speaker, speed, filename, publicId } = params;

        try {
            console.log(`🎤 生成音频: ${filename} (语速: ${speed})`);

            // 构建请求数据
            const audioData = {
                text: text,
                speaker: speaker,
                speed: speed
            };

            // 直接调用GPU实例的音频生成API
            const audioUrl = `https://${publicId}-9880.container.x-gpu.com/`;
console.log("音频URL:"+audioUrl);
            // 创建AbortController用于超时控制
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 300000); // 5分钟超时

            const response = await fetch(audioUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(audioData),
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                // 尝试获取错误响应内容
                let errorText = '';
                try {
                    errorText = await response.text();
                    console.error(`❌ GPU API错误: HTTP ${response.status} - ${errorText}`);
                } catch (e) {
                    console.error(`❌ GPU API错误: HTTP ${response.status} ${response.statusText}`);
                }
                throw new Error(`GPU音频生成API调用失败: HTTP ${response.status} ${response.statusText}${errorText ? ` - ${errorText}` : ''}`);
            }

            // 检查响应是否为音频数据
            const contentType = response.headers.get('content-type');

            if (contentType && contentType.includes('audio/')) {
                // 保存音频文件
                const audioBuffer = await response.arrayBuffer();
                await saveAudioFile(audioBuffer, filename, params.anchorName);

                console.log(`✅ 音频文件 ${filename} 生成成功 (${(audioBuffer.byteLength / 1024).toFixed(1)}KB)`);

                return {
                    success: true,
                    filename: filename,
                    fileSize: audioBuffer.byteLength
                };
            } else {
                // 尝试解析JSON响应
                const responseText = await response.text();

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error(`无法解析GPU API响应: ${responseText}`);
                }

                if (result.success || result.status === 'success') {
                    return {
                        success: true,
                        filename: filename,
                        result: result
                    };
                } else {
                    throw new Error(result.error || result.msg || '音频生成失败');
                }
            }

        } catch (error) {
            console.error('GPU音频生成失败:', error);

            let errorMessage = error.message;

            // 特殊错误处理
            if (error.name === 'AbortError') {
                errorMessage = 'GPU音频生成请求超时（60秒）';
            } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
                errorMessage = `无法连接到GPU实例 ${publicId}，请检查实例状态`;
            } else if (error.message.includes('fetch')) {
                errorMessage = `网络请求失败: ${error.message}`;
            }

            return {
                success: false,
                error: errorMessage
            };
        }
    }







    /**
     * 执行直播任务
     */
    async executeLiveTask(task, params) {
        // TODO: 实现直播任务逻辑
        console.log('直播任务暂未实现');
        throw new Error('直播任务暂未实现');
    }

    /**
     * 更新任务进度
     */
    async updateTaskProgress(taskId, currentProductId, currentSequence) {
        await database.run(`
            UPDATE async_tasks 
            SET current_product_id = ?, current_sequence = ?, updated_at = datetime('now', 'localtime')
            WHERE id = ?
        `, [currentProductId, currentSequence, taskId]);
    }

    /**
     * 更新进度百分比 - 基于子任务状态计算正确的进度
     */
    async updateProgressPercentage(taskId) {
        try {
            console.log(`🔄 开始更新任务 ${taskId} 的进度...`);

            // 获取所有子任务的当前状态
            const allTasksResult = await database.all(
                'SELECT status FROM task_details WHERE task_id = ?',
                [taskId]
            );
            const allTasks = allTasksResult.results || [];

            // 获取任务总数
            const totalTasksResult = await database.get(
                'SELECT COUNT(*) as total FROM task_details WHERE task_id = ?',
                [taskId]
            );
            const totalTasks = totalTasksResult.total || 0;

            // 计算各种状态的数量
            const totalSuccessCount = allTasks.filter(t => t.status === 'completed').length;
            const totalFailedCount = allTasks.filter(t => t.status === 'failed').length;
            const totalCompletedCount = totalSuccessCount + totalFailedCount;
            const progressPercentage = totalTasks > 0 ? Math.round((totalCompletedCount / totalTasks) * 100) : 0;

            console.log(`📊 任务 ${taskId} 进度计算:
            - 总任务数: ${totalTasks}
            - 成功: ${totalSuccessCount}
            - 失败: ${totalFailedCount}
            - 总完成: ${totalCompletedCount}
            - 进度百分比: ${progressPercentage}%`);

            // 更新主任务进度
            await database.run(`
                UPDATE async_tasks
                SET success_count = ?, failed_count = ?, progress_percentage = ?, updated_at = datetime('now', 'localtime')
                WHERE id = ?
            `, [totalSuccessCount, totalFailedCount, progressPercentage, taskId]);

            console.log(`✅ 任务 ${taskId} 进度已更新为 ${progressPercentage}%`);

        } catch (error) {
            console.error('更新任务进度失败:', error);
        }
    }



    /**
     * 等待容器就绪
     */
    async waitForContainerReady(instanceId) {
        const containerUrl = `https://${instanceId}-9880.container.x-gpu.com`;
        let isReady = false;
        let retryCount = 0;
        const maxRetries = 600; // 最大等待10分钟

        while (!isReady && retryCount < maxRetries) {
            try {
                // 创建AbortController用于超时控制
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

                const response = await fetch(containerUrl, {
                    method: 'GET',
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (response.status === 200) {
                    isReady = true;
                    console.log(`✅ 容器已就绪: ${containerUrl}`);
                    // 延迟5秒后返回
                    await new Promise(resolve => setTimeout(resolve, 5000));
                    break;
                } else {
                    retryCount++;
                    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
                }
            } catch (error) {
                retryCount++;
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        if (!isReady) {
            throw new Error(`容器未能在预期时间内就绪: ${containerUrl}`);
        }
    }

    /**
     * 更新任务当前事件
     */
    async updateTaskCurrentEvent(taskId, currentEvent) {
        try {
            await database.run(
                'UPDATE async_tasks SET current_event = ?, updated_at = datetime(\'now\', \'localtime\') WHERE id = ?',
                [currentEvent, taskId]
            );
        } catch (error) {
            console.warn(`更新任务 ${taskId} 当前事件失败:`, error);
        }
    }

    /**
     * 清除任务当前事件
     */
    async clearTaskCurrentEvent(taskId) {
        try {
            await database.run(
                'UPDATE async_tasks SET current_event = NULL, updated_at = datetime(\'now\', \'localtime\') WHERE id = ?',
                [taskId]
            );
        } catch (error) {
            console.warn(`清除任务 ${taskId} 当前事件失败:`, error);
        }
    }

    /**
     * 标记任务完成
     */
    async markTaskCompleted(taskId) {
        await this.updateTaskStatusSafe(taskId, 'completed', {
            'completed_at': 'NOW()'  // 使用简化的NOW()标记
        });

        // 清除当前事件
        await this.clearTaskCurrentEvent(taskId);

        // 获取任务详情以处理不同类型的完成
        const task = await database.get('SELECT task_type, task_params, live_id, anchor_name, task_name, created_at FROM async_tasks WHERE id = ?', [taskId]);

        // 如果完成的是手卡任务，检查是否需要自动创建音频任务
        if (task && task.task_type === 'hand_card') {
            try {
                if (task.task_params) {
                    const taskParams = JSON.parse(task.task_params);
                    if (taskParams.autoCreateAudioTask) {
                        console.log(`🎵 手卡任务完成，开始自动创建音频任务 - 任务ID: ${taskId}, 直播ID: ${task.live_id}`);
                        await this.autoCreateAudioTask(task.live_id, task.anchor_name);
                    }
                }
            } catch (error) {
                console.warn('手卡任务完成时自动创建音频任务失败:', error);
            }
        }

        // 如果完成的是音频任务，检查是否需要自动销毁GPU实例并发送钉钉通知
        if (task && task.task_type === 'audio') {
            try {
                if (task.task_params) {
                    const taskParams = JSON.parse(task.task_params);
                    await handleGpuInstanceDestroy(taskParams, 'completed');
                }
            } catch (error) {
                console.warn('音频任务完成时自动销毁GPU实例失败:', error);
            }

            // 发送音频任务完成钉钉通知
            try {
                console.log(`📱 开始发送音频任务完成钉钉通知 - 任务ID: ${taskId}`);
                await this.sendAudioTaskCompletionNotification({
                    id: taskId,
                    task_name: task.task_name,
                    anchor_name: task.anchor_name,
                    created_at: task.created_at
                });
            } catch (error) {
                console.warn('音频任务完成时发送钉钉通知失败:', error);
            }
        }

        // 任务完成后，检查是否有其他待执行任务
        console.log(`✅ 任务 ${taskId} 已完成，检查是否有其他待执行任务...`);

        // 立即检查并启动其他待执行任务
        setTimeout(() => {
            this.processNextTask();
        }, 500);
    }

    /**
     * 标记任务失败（增强版本）
     * @param {number} taskId 任务ID
     * @param {string} errorMessage 错误消息
     * @param {boolean} shouldRetry 是否应该重试
     */
    async markTaskFailed(taskId, errorMessage, shouldRetry = false) {
        try {
            // 获取任务详情
            const task = await database.get('SELECT task_type, task_name, anchor_name, retry_count, max_retries, created_at FROM async_tasks WHERE id = ?', [taskId]);

            if (!task) {
                console.error(`任务不存在: ${taskId}`);
                return;
            }

            let newStatus = 'failed';
            let retryCount = task.retry_count || 0;

            // 检查是否应该重试
            if (shouldRetry && retryCount < (task.max_retries || 3)) {
                newStatus = 'pending';
                retryCount += 1;
                console.log(`🔄 任务 ${taskId} 将进行第 ${retryCount} 次重试`);
            } else {
                console.log(`❌ 任务 ${taskId} 最终失败: ${errorMessage}`);
            }

            // 更新任务状态
            await this.updateTaskStatusSafe(taskId, newStatus, {
                'error_message': errorMessage,
                'retry_count': retryCount
            });

            // 如果是最终失败，执行清理逻辑
            if (newStatus === 'failed') {
                // 清除当前事件
                await this.clearTaskCurrentEvent(taskId);

                // 如果是音频任务最终失败，发送钉钉通知
                if (task.task_type === 'audio') {
                    try {
                        console.log(`📱 开始发送音频任务失败钉钉通知 - 任务ID: ${taskId}`);
                        await this.sendAudioTaskFailureNotification({
                            id: taskId,
                            task_name: task.task_name,
                            anchor_name: task.anchor_name,
                            error_message: errorMessage,
                            created_at: task.created_at
                        });
                    } catch (error) {
                        console.warn('音频任务失败时发送钉钉通知失败:', error);
                    }
                }

                console.log(`❌ 任务 ${taskId} 执行失败`);
            } else if (newStatus === 'pending') {
                // 如果是重试，重新加入处理队列
                setTimeout(() => {
                    this.processNextTask();
                }, 2000); // 2秒后重试
            }

        } catch (error) {
            console.error('标记任务失败时出错:', error);
        }
    }

    /**
     * 取消任务
     */
    async cancelTask(taskId) {
        // 获取任务信息
        const task = await database.get('SELECT task_type, status FROM async_tasks WHERE id = ?', [taskId]);
        
        if (!task) {
            throw new Error('任务不存在');
        }

        // 检查任务是否可以取消
        const cancellableStatuses = ['pending', 'running', 'paused'];
        if (!cancellableStatuses.includes(task.status)) {
            console.log(`⚠️ 任务 ${taskId} 状态为 ${task.status}，无法取消`);
            return false;
        }

        // 在事务中原子性更新主任务和子任务
        await database.transaction(async () => {
            // 更新主任务状态（带条件检查）
            await this.updateTaskStatusSafe(taskId, 'cancelled', {}, 
                ["status IN ('pending', 'running', 'paused')"]
            );

            // 取消所有待执行的子任务
            await database.run(`
                UPDATE task_details
                SET status = 'cancelled'
                WHERE task_id = ? AND status = 'pending'
            `, [taskId]);
        });

        console.log(`🚫 任务 ${taskId} 已取消 (状态: ${task.status} -> cancelled)`);

        // 清除当前事件
        await this.clearTaskCurrentEvent(taskId);

        // 如果取消的是音频任务，检查是否需要销毁GPU实例
        if (task && task.task_type === 'audio') {
            try {
                const fullTask = await database.get('SELECT task_params FROM async_tasks WHERE id = ?', [taskId]);
                if (fullTask && fullTask.task_params) {
                    const taskParams = JSON.parse(fullTask.task_params);
                    await handleGpuInstanceDestroy(taskParams, 'cancelled');
                }
            } catch (error) {
                console.warn('取消音频任务时销毁GPU实例失败:', error);
            }
        }

        // 任务取消后，检查是否有其他待执行任务
        console.log(`🚫 任务 ${taskId} 已取消，检查是否有其他待执行任务...`);

        // 立即检查并启动其他待执行任务
        setTimeout(() => {
            this.processNextTask();
        }, 500);

        return true;
    }

    /**
     * 暂停任务
     */
    async pauseTask(taskId) {
        try {
            // 检查任务是否可以暂停
            const task = await database.get(`
                SELECT id, status, task_name, task_type
                FROM async_tasks
                WHERE id = ?
            `, [taskId]);

            if (!task) {
                throw new Error('任务不存在');
            }

            if (task.status !== 'running') {
                throw new Error(`任务状态为 ${task.status}，无法暂停`);
            }

            // 更新任务状态为暂停
            await this.updateTaskStatusSafe(taskId, 'paused', {
                'paused_at': 'NOW()'  // 使用简化的NOW()标记
            });

            console.log(`⏸️ 任务已暂停: ${task.task_name} (ID: ${taskId})`);

            // 任务暂停后，检查是否有其他待执行任务
            console.log(`⏸️ 任务 ${taskId} 已暂停，检查是否有其他待执行任务...`);

            // 立即检查并启动其他待执行任务
            setTimeout(() => {
                this.processNextTask();
            }, 500);

            return true;

        } catch (error) {
            console.error('暂停任务失败:', error);
            throw error;
        }
    }

    /**
     * 恢复任务
     */
    async resumeTask(taskId) {
        try {
            // 检查任务是否可以恢复
            const task = await database.get(`
                SELECT id, status, task_name, task_type
                FROM async_tasks
                WHERE id = ?
            `, [taskId]);

            if (!task) {
                throw new Error('任务不存在');
            }

            if (task.status !== 'paused') {
                throw new Error(`任务状态为 ${task.status}，无法恢复`);
            }

            // 重置所有进行中的子任务状态为待处理（暂停时可能有子任务处于in_progress状态）
            await database.run(`
                UPDATE task_details
                SET status = 'pending', started_at = NULL
                WHERE task_id = ? AND status = 'in_progress'
            `, [taskId]);

            const resetCount = await database.get(`
                SELECT COUNT(*) as count FROM task_details
                WHERE task_id = ? AND status = 'pending'
            `, [taskId]);

            console.log(`🔄 重置了进行中的子任务，当前待处理子任务数: ${resetCount.count || 0}`);

            // 更新任务状态为待执行
            await this.updateTaskStatusSafe(taskId, 'pending', {
                'paused_at': null  // 使用真正的null
            });

            console.log(`▶️ 任务已恢复: ${task.task_name} (ID: ${taskId})`);

            // 尝试启动任务处理
            this.processNextTask();

            return true;

        } catch (error) {
            console.error('恢复任务失败:', error);
            throw error;
        }
    }

    /**
     * 为任务更新主播Cookie
     * @param {string} anchorName - 主播名称
     * @returns {Promise<object>} - 更新结果
     */
    async updateAnchorCookieForTask(anchorName) {
        try {
            console.log(`🔄 开始为任务更新主播 ${anchorName} 的Cookie...`);

            // 获取主播信息
            const anchor = await database.get(
                'SELECT * FROM anchors WHERE anchor_name = ?',
                [anchorName]
            );

            if (!anchor) {
                return {
                    success: false,
                    error: '未找到主播信息'
                };
            }

            // 调用job.js中的cookie更新函数
            const { updateAnchorCookieFromAPI } = await import('./job.js');
            const result = await updateAnchorCookieFromAPI(anchor);

            if (result.success) {
                console.log(`✅ 主播 ${anchorName} 的Cookie更新成功`);
                return {
                    success: true,
                    message: result.message || 'Cookie更新成功'
                };
            } else {
                console.log(`❌ 主播 ${anchorName} 的Cookie更新失败: ${result.error}`);
                return {
                    success: false,
                    error: result.error || 'Cookie更新失败'
                };
            }
        } catch (error) {
            console.error(`❌ 更新主播 ${anchorName} Cookie时发生异常:`, error);
            return {
                success: false,
                error: error.message || '更新Cookie时发生异常'
            };
        }
    }

    /**
     * 重试失败任务
     */
    async retryTask(taskId) {
        const task = await database.get('SELECT * FROM async_tasks WHERE id = ?', [taskId]);

        if (!task) {
            throw new Error('任务不存在');
        }

        if (task.retry_count >= task.max_retries) {
            throw new Error('已达到最大重试次数');
        }

        // 在事务中重置任务状态和子任务
        await database.transaction(async () => {
            // 重置主任务状态（确保类型安全）
            const newRetryCount = (task.retry_count || 0) + 1;
            await this.updateTaskStatusSafe(taskId, 'pending', {
                'retry_count': newRetryCount,
                'error_message': null  // 使用真正的null
            });

            // 重置失败的子任务
            await database.run(`
                UPDATE task_details
                SET status = 'pending', error_message = NULL
                WHERE task_id = ? AND status = 'failed'
            `, [taskId]);
        });

        // 尝试重新执行
        this.processNextTask();
    }

    /**
     * 获取当前执行状态
     */
    async getCurrentStatus() {
        const runningTasks = await database.all(`
            SELECT id, task_name, task_type, anchor_name
            FROM async_tasks
            WHERE status = 'running'
            ORDER BY started_at ASC
        `);

        const tasks = runningTasks.results || [];
        
        return {
            isProcessing: tasks.length > 0,
            currentTask: tasks.length > 0 ? {
                id: tasks[0].id,
                name: tasks[0].task_name,
                type: tasks[0].task_type,
                anchorName: tasks[0].anchor_name
            } : null,
            processingTaskCount: tasks.length,
            processingTasks: tasks.map(task => ({
                id: task.id,
                name: task.task_name,
                type: task.task_type,
                anchorName: task.anchor_name
            }))
        };
    }

    /**
     * 生成产品ID文件
     */
    async generateProductIdFile(task, anchorName) {
        try {
            console.log(`📝 开始生成产品ID文件...`);

            // 获取任务中所有成功完成音频生成的产品，按序号倒序排列
            const detailsResult = await database.all(`
                SELECT td.product_id, td.sequence_number
                FROM task_details td
                WHERE td.task_id = ?
                    AND td.status = 'completed'
                ORDER BY td.sequence_number DESC
            `, [task.id]);

            const details = detailsResult.results || [];

            console.log(`🔍 找到 ${details.length} 个已完成的子任务`);

            if (details.length === 0) {
                console.log(`⚠️ 任务 ${task.id} 没有成功完成的子任务，跳过生成产品ID文件`);
                return;
            }

            // 提取产品ID列表
            const productIds = details.map(detail => detail.product_id);

            // 获取音频文件存储目录（与音频文件保存在同一位置）
            const outputDir = await getAudioDirectoryPath(anchorName);
            
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }

            // 生成文件名（格式：产品ID_2025-07-07_17_53.txt）
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const timestamp = `${year}-${month}-${day}_${hours}_${minutes}`;
            const filename = `产品ID_${timestamp}.txt`;
            const filePath = path.join(outputDir, filename);

            // 写入产品ID到文件
            const content = productIds.join('\n');
            fs.writeFileSync(filePath, content, 'utf8');

            console.log(`✅ 产品ID文件生成成功: ${filePath}`);
            console.log(`📊 共包含 ${productIds.length} 个产品ID（按序号倒序排列）`);

        } catch (error) {
            console.error('生成产品ID文件失败:', error);
        }
    }

    /**
     * 自动创建音频任务
     * @param {string} liveId 直播ID
     * @param {string} anchorName 主播名称
     */
    async autoCreateAudioTask(liveId, anchorName) {
        try {
            console.log(`🎵 开始自动创建音频任务 - 直播ID: ${liveId}, 主播: ${anchorName}`);

            // 验证必要参数
            if (!liveId || !anchorName) {
                throw new Error('直播ID和主播名称不能为空');
            }

            // 检查是否已有该直播的音频任务正在执行或待执行
            const existingAudioTask = await database.get(`
                SELECT id, status FROM async_tasks
                WHERE live_id = ? AND task_type = 'audio' AND status IN ('pending', 'running')
                ORDER BY created_at DESC LIMIT 1
            `, [liveId]);

            if (existingAudioTask) {
                console.log(`⚠️ 直播 ${liveId} 已有音频任务正在执行 (任务ID: ${existingAudioTask.id}, 状态: ${existingAudioTask.status})，跳过自动创建`);
                return;
            }

            // 使用默认音频参数
            const audioParams = {
                liveId: liveId,
                startSequence: 1,
                endSequence: 420,
                speed: 'random',
                randomSpeed: true,
                autoDestroy: true,
                concurrency: 20,
                autoCreateGpu: true,
                anchorName: anchorName
            };

            // 直接调用音频生成API模块，使用与手动创建相同的逻辑
            const { createAudioGenerationTask, getProductsBySequenceRange, createProductAudioTask, checkAudioDirectory, cleanupAudioDirectory } = await import('./api/gpu.js');

            // 查询产品数据
            console.log(`🔍 查询产品数据 - 直播ID: ${liveId}, 序号范围: ${audioParams.startSequence}-${audioParams.endSequence}`);
            const products = await getProductsBySequenceRange(liveId, audioParams.startSequence, audioParams.endSequence);

            if (products.length === 0) {
                console.log(`⚠️ 未找到指定序号范围内的产品 - 直播ID: ${liveId}, 序号范围: ${audioParams.startSequence}-${audioParams.endSequence}`);
                throw new Error('未找到指定序号范围内的产品');
            }

            console.log(`📋 找到 ${products.length} 个有手卡内容的产品`);

            // 检测并清空主播音频文件目录
            await this.checkAndCleanupAudioDirectory(anchorName, checkAudioDirectory, cleanupAudioDirectory);

            // 创建任务记录，使用与手动创建相同的参数结构
            const taskParams = {
                speed: audioParams.speed === 'random' ? 1.2 : audioParams.speed,
                randomSpeed: audioParams.randomSpeed,
                autoDestroy: audioParams.autoDestroy,
                concurrency: audioParams.concurrency,
                autoCreateGpu: audioParams.autoCreateGpu,
                instanceId: null,
                publicId: null,
                anchorName: audioParams.anchorName,
                productCount: products.length
            };

            const taskId = await createAudioGenerationTask(liveId, 'audio', taskParams);

            // 为每个产品创建子任务，使用与手动创建相同的逻辑
            for (const product of products) {
                await createProductAudioTask(taskId, product, taskParams);
            }

            // 触发任务管理器立即处理新任务
            this.processNextTask();

            console.log(`✅ 自动创建音频任务成功 - 任务ID: ${taskId}, 直播ID: ${liveId}, 产品数量: ${products.length}`);

        } catch (error) {
            console.error(`❌ 自动创建音频任务失败 - 直播ID: ${liveId}, 错误:`, error.message);
            // 不抛出错误，避免影响主任务流程
        }
    }

    /**
     * 检测并清空主播音频文件目录
     * @param {string} anchorName 主播名称
     * @param {Function} checkAudioDirectory 检查目录函数
     * @param {Function} cleanupAudioDirectory 清理目录函数
     */
    async checkAndCleanupAudioDirectory(anchorName, checkAudioDirectory, cleanupAudioDirectory) {
        try {
            console.log(`🗂️ 检测主播音频文件目录 - 主播: ${anchorName}`);

            // 调用已有的检查目录函数
            const checkResult = await checkAudioDirectory(anchorName);

            if (checkResult.hasFiles && checkResult.fileCount > 0) {
                console.log(`📁 发现 ${checkResult.fileCount} 个音频文件，开始清空目录`);

                // 自动清空目录
                const cleanupResult = await cleanupAudioDirectory(anchorName);

                if (cleanupResult.success) {
                    console.log(`✅ 目录清空成功，删除了 ${cleanupResult.deletedCount} 个文件`);
                } else {
                    console.warn(`⚠️ 目录清空失败，但继续执行任务`);
                }
            } else {
                console.log(`📂 目录为空或不存在，无需清理`);
            }

        } catch (error) {
            console.warn(`⚠️ 检测/清空目录失败，但继续执行任务:`, error.message);
            // 不抛出错误，避免影响主任务流程
        }
    }

    /**
     * 获取GPU账户的钉钉Key
     * 获取gpu_accounts表中的第一条数据的ding_key
     */
    async getGpuAccountDingKey() {
        try {
            const result = await database.get(`
                SELECT ding_key
                FROM gpu_accounts
                WHERE status = 'active' AND ding_key IS NOT NULL AND ding_key != ''
                ORDER BY id ASC
                LIMIT 1
            `);
            return result?.ding_key || null;
        } catch (error) {
            console.error('获取GPU账户钉钉Key失败:', error);
            return null;
        }
    }

    /**
     * 发送音频任务完成钉钉通知
     */
    async sendAudioTaskCompletionNotification(task) {
        try {
            // 获取钉钉Key
            const dingKey = await this.getGpuAccountDingKey();
            if (!dingKey) {
                console.log('未找到可用的钉钉Key，跳过音频任务完成通知发送');
                return;
            }

            // 获取任务统计信息
            const statsResult = await database.get(`
                SELECT
                    COUNT(*) as total_count,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as success_count,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count
                FROM task_details
                WHERE task_id = ?
            `, [task.id]);

            const stats = statsResult || { total_count: 0, success_count: 0, failed_count: 0 };

            // 构建钉钉消息内容
            const message = this.buildAudioTaskCompletionMessage(task, stats);

            // 发送钉钉消息
            await this.sendDingTalkMessage(dingKey, message);
            console.log('音频任务完成钉钉通知发送成功');

        } catch (error) {
            console.error('发送音频任务完成钉钉通知失败:', error);
            // 不抛出错误，避免影响主任务流程
        }
    }

    /**
     * 构建音频任务完成通知消息内容
     */
    buildAudioTaskCompletionMessage(task, stats) {
        const currentTime = new Date();
        const currentTimeStr = `${currentTime.getFullYear()}-${String(currentTime.getMonth() + 1).padStart(2, '0')}-${String(currentTime.getDate()).padStart(2, '0')} ${String(currentTime.getHours()).padStart(2, '0')}:${String(currentTime.getMinutes()).padStart(2, '0')}:${String(currentTime.getSeconds()).padStart(2, '0')}`;

        // 计算任务耗时（分钟）
        let durationText = '未知';
        if (task.created_at) {
            try {
                const createdTime = new Date(task.created_at);
                const durationMs = currentTime.getTime() - createdTime.getTime();
                const durationMinutes = Math.round(durationMs / (1000 * 60));
                durationText = `${durationMinutes}分钟`;
            } catch (error) {
                console.warn('计算任务耗时失败:', error);
            }
        }

        const content = `🎵 **[音频任务完成通知]**\n\n` +
                       `👤 主播：${task.anchor_name}\n` +
                       `📊 任务统计：\n` +
                       `   • 总数：${stats.total_count}\n` +
                       `   • 成功：${stats.success_count}\n` +
                       `   • 失败：${stats.failed_count}\n` +
                       `⏱️ 耗时：${durationText}\n` +
                       `✅ 完成时间：${currentTimeStr}\n`;

        return {
            msgtype: "text",
            text: {
                content: content
            }
        };
    }

    /**
     * 发送音频任务失败钉钉通知
     */
    async sendAudioTaskFailureNotification(task) {
        try {
            // 获取钉钉Key
            const dingKey = await this.getGpuAccountDingKey();
            if (!dingKey) {
                console.log('未找到可用的钉钉Key，跳过音频任务失败通知发送');
                return;
            }

            // 获取任务统计信息
            const statsResult = await database.get(`
                SELECT
                    COUNT(*) as total_count,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as success_count,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count
                FROM task_details
                WHERE task_id = ?
            `, [task.id]);

            const stats = statsResult || { total_count: 0, success_count: 0, failed_count: 0 };

            // 构建钉钉消息内容
            const message = this.buildAudioTaskFailureMessage(task, stats);

            // 发送钉钉消息
            await this.sendDingTalkMessage(dingKey, message);
            console.log('音频任务失败钉钉通知发送成功');

        } catch (error) {
            console.error('发送音频任务失败钉钉通知失败:', error);
            // 不抛出错误，避免影响主任务流程
        }
    }

    /**
     * 构建音频任务失败通知消息内容
     */
    buildAudioTaskFailureMessage(task, stats) {
        const currentTime = new Date();
        const currentTimeStr = `${currentTime.getFullYear()}-${String(currentTime.getMonth() + 1).padStart(2, '0')}-${String(currentTime.getDate()).padStart(2, '0')} ${String(currentTime.getHours()).padStart(2, '0')}:${String(currentTime.getMinutes()).padStart(2, '0')}:${String(currentTime.getSeconds()).padStart(2, '0')}`;

        // 计算任务耗时（分钟）
        let durationText = '未知';
        if (task.created_at) {
            try {
                const createdTime = new Date(task.created_at);
                const durationMs = currentTime.getTime() - createdTime.getTime();
                const durationMinutes = Math.round(durationMs / (1000 * 60));
                durationText = `${durationMinutes}分钟`;
            } catch (error) {
                console.warn('计算任务耗时失败:', error);
            }
        }

        const content = `❌ **[音频任务失败通知]**\n\n` +
                       `📋 任务名称：${task.task_name}\n` +
                       `👤 主播：${task.anchor_name}\n` +
                       `📊 任务统计：\n` +
                       `   • 总数：${stats.total_count}\n` +
                       `   • 成功：${stats.success_count}\n` +
                       `   • 失败：${stats.failed_count}\n` +
                       `⏱️ 耗时：${durationText}\n` +
                       `⚠️ 失败原因：${task.error_message || '未知错误'}\n` +
                       `🕐 失败时间：${currentTimeStr}\n` +
                       `📝 说明：音频生成任务执行失败，请检查相关配置`;

        return {
            msgtype: "text",
            text: {
                content: content
            }
        };
    }

    /**
     * 发送钉钉消息
     */
    async sendDingTalkMessage(dingKey, message) {
        try {
            const url = `https://oapi.dingtalk.com/robot/send?access_token=${dingKey}`;

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(message)
            });

            const result = await response.json();

            if (result.errcode === 0) {
                console.log('钉钉消息发送成功');
            } else {
                console.error('钉钉消息发送失败:', result);
                throw new Error(`钉钉API错误: ${result.errmsg || '未知错误'}`);
            }

            return result;
        } catch (error) {
            console.error('发送钉钉消息失败:', error);
            throw error;
        }
    }


}

// 创建单例实例
const taskManager = new TaskManager();

export default taskManager;
