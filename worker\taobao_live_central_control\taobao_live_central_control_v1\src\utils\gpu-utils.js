/**
 * GPU工具模块 - 统一管理GPU相关功能
 * 避免重复代码，提供统一的GPU账户和实例管理功能
 */

import database from '../database.js';

/**
 * 获取可用的GPU账户
 * 按最后使用时间排序，优先使用最久未使用的账户
 */
export async function getAvailableGpuAccount() {
    try {
        const result = await database.get(`
            SELECT id, account, token, api_key, image_id
            FROM gpu_accounts
            WHERE status = 'active' AND (
                (token IS NOT NULL AND token != '') OR
                (api_key IS NOT NULL AND api_key != '')
            )
            ORDER BY
                CASE WHEN last_used_at IS NULL THEN 0 ELSE 1 END,
                last_used_at ASC
            LIMIT 1
        `);
        return result;
    } catch (error) {
        console.error('获取GPU账户失败:', error);
        return null;
    }
}

/**
 * 销毁GPU实例 (统一使用新的API接口)
 * @param {string} publicId - 公共ID
 * @param {string} apiKey - API密钥
 * @returns {Promise<boolean>} 是否成功销毁
 */
export async function destroyGpuInstance(publicId, apiKey) {
    try {
        console.log(`🗑️ 准备销毁GPU实例: ${publicId}, API Key: ${apiKey}`);

        const response = await fetch(`${XIANGONGYUN_CONFIG.baseUrl}/open/instance/destroy`, {
            method: 'POST',
            headers: {
                ...XIANGONGYUN_CONFIG.headers,
                'authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                id: publicId
            })
        });

        if (!response.ok) {
            throw new Error(`销毁GPU实例失败: HTTP ${response.status}`);
        }

        const result = await response.json();
        console.log(`🔍 销毁GPU实例响应:`, result);

        if (result.code !== 200 || !result.success) {
            throw new Error(`销毁实例返回错误: ${result.msg || '未知错误'}`);
        }

        console.log(`✅ 成功销毁GPU实例: ${publicId}`);
        return true;

    } catch (error) {
        console.error('销毁GPU实例失败:', error);
        throw error;
    }
}

/**
 * 统一的GPU实例销毁处理逻辑
 * @param {Object} taskParams - 任务参数
 * @param {string} reason - 销毁原因 (completed/cancelled/stopped)
 * @param {number} delay - 延迟销毁时间(毫秒)，默认0
 * @returns {Promise<boolean>} 是否成功销毁
 */
export async function handleGpuInstanceDestroy(taskParams, reason = 'unknown', delay = 0) {
    try {
        const { autoDestroy, instanceId, publicId, autoCreateGpu } = taskParams;

        // 检查是否需要销毁GPU实例
        if (!(autoDestroy || autoCreateGpu) || !(publicId || instanceId)) {
            return false;
        }

        const destroyId = publicId || instanceId;
        console.log(`🗑️ 准备销毁GPU实例: ${destroyId} (原因: ${reason})`);

        const destroyFunction = async () => {
            try {
                const gpuAccount = await getAvailableGpuAccount();
                if (gpuAccount && gpuAccount.api_key) {
                    await destroyGpuInstance(destroyId, gpuAccount.api_key);
                    console.log(`✅ GPU实例 ${destroyId} 已销毁 (原因: ${reason})`);
                    return true;
                } else {
                    console.warn(`⚠️ 无法获取GPU账户信息，跳过销毁: ${destroyId}`);
                    return false;
                }
            } catch (error) {
                console.warn(`⚠️ 销毁GPU实例失败: ${destroyId}`, error.message);
                return false;
            }
        };

        if (delay > 0) {
            console.log(`⏰ GPU实例 ${destroyId} 将在${delay/1000}秒后销毁`);
            setTimeout(destroyFunction, delay);
            return true; // 异步销毁，返回true表示已安排销毁
        } else {
            return await destroyFunction();
        }

    } catch (error) {
        console.error('处理GPU实例销毁失败:', error);
        return false;
    }
}



/**
 * 更新GPU账户最后使用时间
 * @param {number} accountId - 账户ID
 */
export async function updateGpuAccountLastUsed(accountId) {
    try {
        await database.run(`
            UPDATE gpu_accounts
            SET last_used_at = datetime('now', 'localtime'),
                updated_at = datetime('now', 'localtime')
            WHERE id = ?
        `, [accountId]);
    } catch (error) {
        console.warn('更新GPU账户使用时间失败:', error);
    }
}

/**
 * 统一的GPU实例创建函数
 * @param {string} anchorName - 主播名称
 * @param {Object} options - 创建选项
 * @param {number} options.maxRetries - 最大重试次数，默认100
 * @param {number} options.retryDelay - 重试间隔(毫秒)，默认100
 * @returns {Promise<Object>} 返回 {instanceId, publicId}
 */
export async function createGpuInstance(anchorName, options = {}) {
    const { maxRetries = 100, retryDelay = 100 } = options;

    const gpuAccount = await getAvailableGpuAccount();
    if (!gpuAccount || !gpuAccount.api_key) {
        throw new Error('无法获取可用的GPU账户或API密钥');
    }

    // 获取image_id - 从GPU账户中第一条信息获取
    let imageId = '260cd46a-12aa-44ed-b47a-88f770b49041'; // 默认值
    try {
        const firstGpuAccount = await database.get(
            'SELECT image_id FROM gpu_accounts WHERE status = ? AND image_id IS NOT NULL AND image_id != ? ORDER BY id ASC LIMIT 1',
            ['active', '']
        );
        if (firstGpuAccount && firstGpuAccount.image_id) {
            imageId = firstGpuAccount.image_id;
        }
    } catch (error) {
        console.warn('获取GPU账户image_id失败，使用默认值:', error);
    }

    const createParams = {
        ...XIANGONGYUN_CONFIG.defaultCreateParams,
        image: imageId,
        name: `${anchorName}-使用中`
    };

    let instanceId = null;
    let retryCount = 0;

    while (!instanceId && retryCount < maxRetries) {
        try {
            const response = await fetch(`${XIANGONGYUN_CONFIG.baseUrl}/open/instance/deploy`, {
                method: 'POST',
                headers: {
                    ...XIANGONGYUN_CONFIG.headers,
                    'authorization': `Bearer ${gpuAccount.api_key}`
                },
                body: JSON.stringify(createParams)
            });

            const result = await response.json();

            if (result.success && result.data && result.data.id) {
                instanceId = result.data.id;
                console.log(`✅ GPU实例创建成功: ${instanceId}`);
                break;
            } else {
                console.log(`⚠️ GPU创建失败，重试中... (${retryCount + 1}/${maxRetries}): ${result.msg || '未知错误'}`);
                retryCount++;
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        } catch (error) {
            console.error(`GPU创建请求失败: ${error.message}`);
            retryCount++;
            await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
    }

    if (!instanceId) {
        throw new Error(`GPU实例创建失败，已重试 ${maxRetries} 次`);
    }

    // 生成publicId (实际上就是instanceId)
    const publicId = instanceId;

    return {
        instanceId,
        publicId
    };
}

/**
 * 象工云API配置
 */
export const XIANGONGYUN_CONFIG = {
    baseUrl: 'https://api.xiangongyun.com',
    headers: {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'content-type': 'application/json',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'referrer': 'https://www.xiangongyun.com/',
        'referrerPolicy': 'strict-origin-when-cross-origin'
    },
    // GPU实例创建默认配置
    defaultCreateParams: {
        gpu_model: "NVIDIA GeForce RTX 4090 D",
        gpu_count: 1,
        data_center_id: 1,
        image_type: "private",
        storage: false,
        storage_mount_path: "/root/cloud",
        system_disk_expand: false,
        system_disk_expand_size: 0
    }
};

/**
 * 调用象工云API获取实例列表
 * @param {string} token - GPU账户token
 * @returns {Promise<Array>} 实例列表
 */
export async function fetchXiangongyunInstances(token) {
    try {
        const response = await fetch(`${XIANGONGYUN_CONFIG.baseUrl}/api/instance/list`, {
            method: 'POST',
            headers: {
                ...XIANGONGYUN_CONFIG.headers,
                'authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                page: 1,
                pageSize: 30
            })
        });

        if (!response.ok) {
            throw new Error(`象工云API请求失败: HTTP ${response.status}`);
        }

        const result = await response.json();
        
        if (result.code !== 200 || !result.success) {
            throw new Error(`象工云API返回错误: ${result.msg || '未知错误'}`);
        }

        return result.data?.list || [];

    } catch (error) {
        console.error('调用象工云API失败:', error);
        throw error;
    }
}

/**
 * 筛选可用实例
 * @param {Array} instances - 实例列表
 * @returns {Object|null} 可用实例
 */
export function findAvailableInstance(instances) {
    return instances.find(instance => {
        // 必须是运行中状态
        if (instance.status !== 1) {
            return false;
        }

        // 名称为空或不包含"使用中"
        if (!instance.name || !instance.name.includes('使用中')) {
            return true;
        }

        return false;
    });
}

/**
 * 更新实例名称
 * @param {string} instanceId - 实例ID
 * @param {string} newName - 新名称
 * @param {string} token - GPU账户token
 * @returns {Promise<boolean>} 是否成功更新
 */
export async function updateInstanceName(instanceId, newName, token) {
    try {
        const response = await fetch(`${XIANGONGYUN_CONFIG.baseUrl}/api/instance/name`, {
            method: 'POST',
            headers: {
                ...XIANGONGYUN_CONFIG.headers,
                'authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                id: instanceId,
                name: newName
            })
        });

        if (!response.ok) {
            console.warn(`更新实例名称失败: HTTP ${response.status}`);
            return false;
        }

        const result = await response.json();
        return result.code === 200 && result.success;

    } catch (error) {
        console.warn('更新实例名称异常:', error);
        return false;
    }
}

/**
 * 获取音频文件存储目录路径
 * @param {string} anchorName - 主播名称
 * @returns {Promise<string>} 目录路径
 */
export async function getAudioDirectoryPath(anchorName) {
    const os = await import('os');
    const path = await import('path');
    const isWindows = os.platform() === 'win32';

    if (isWindows) {
        return path.join('D:', 'voice', anchorName);
    } else {
        return path.join('/taobao', 'voice', anchorName);
    }
}

/**
 * 保存音频文件
 * @param {ArrayBuffer} audioBuffer - 音频数据
 * @param {string} filename - 文件名
 * @param {string} anchorName - 主播名称
 * @returns {Promise<string>} 保存的文件路径
 */
export async function saveAudioFile(audioBuffer, filename, anchorName) {
    const fs = await import('fs/promises');
    const path = await import('path');

    try {
        // 获取目录路径
        const anchorDir = await getAudioDirectoryPath(anchorName);

        // 确保目录存在
        await fs.mkdir(anchorDir, { recursive: true });

        // 构建完整文件路径
        const filePath = path.join(anchorDir, filename);

        // 保存文件
        await fs.writeFile(filePath, Buffer.from(audioBuffer));

        console.log(`💾 音频文件已保存: ${filePath}`);
        return filePath;

    } catch (error) {
        console.error('保存音频文件失败:', error);
        throw error;
    }
}

/**
 * 处理手卡内容
 * 支持两种类型的手卡处理：
 * 1. 商品卖点类型：移除序号标记和【】中的内容
 * 2. 讲解脚本类型：只提取"### 商品讲解"部分的内容
 * @param {string} scriptContent - 原始手卡内容
 * @returns {string} 处理后的内容
 */
export function processScriptContent(scriptContent) {
    if (!scriptContent) return '';

    // 检查是否为讲解脚本类型（包含### 标题）
    if (scriptContent.includes('### ')) {
        // 第二种类型：讲解脚本，只提取"### 商品讲解"部分
        const sections = scriptContent.split(/###\s*/);

        // 查找"商品讲解"部分
        for (let i = 0; i < sections.length; i++) {
            const section = sections[i].trim();
            if (section.startsWith('商品讲解')) {
                // 提取商品讲解部分的内容（去掉标题）
                const content = section.replace(/^商品讲解\s*/, '').trim();
                return content
                    .replace(/\n+/g, ' ') // 将换行符替换为空格
                    .trim(); // 去掉首尾空格
            }
        }

        // 如果没有找到"商品讲解"部分，返回空字符串
        console.warn('未找到"### 商品讲解"部分，手卡内容可能格式不正确');
        return '';
    } else {
        // 第一种类型：商品卖点，移除序号标记和【】中的内容
        return scriptContent
            .replace(/【[^】]*】/g, '') // 去掉【xx】
            .replace(/^\d+\.\s*/gm, '') // 去掉行首的数字序号（如"1. "）
            .replace(/\n+/g, ' ') // 将换行符替换为空格
            .trim(); // 去掉首尾空格
    }
}
